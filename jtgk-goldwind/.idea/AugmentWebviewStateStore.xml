<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;fa5748e6-8809-449e-97ad-dc20d4bf3bbb&quot;,&quot;conversations&quot;:{&quot;fa5748e6-8809-449e-97ad-dc20d4bf3bbb&quot;:{&quot;id&quot;:&quot;fa5748e6-8809-449e-97ad-dc20d4bf3bbb&quot;,&quot;name&quot;:&quot;Welcome to the Augment Agent&quot;,&quot;createdAtIso&quot;:&quot;2025-06-05T06:38:13.562Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-06T07:15:15.789Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;54ba1e8a-9d6b-46ff-ace9-a3e9595c130d&quot;,&quot;uuid&quot;:&quot;6beb3181-2103-4086-804c-7523dd66cfd1&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1749105493563,&quot;seen_state&quot;:&quot;seen&quot;}],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;0b09f011-30b7-44ee-9f6d-ae575da65633&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>