package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.SchedulerLoggerUtil;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.lockservice.api.*;
import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.alibaba.fastjson.JSON;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@GspServiceBundle(applicationName = "jtgk", serviceUnitName = "goldwind", serviceName = "JfskSettlementAutoDealController")
public class JfskSettlementAutoDealController {

    @Autowired
    private LogService logService;

    @Autowired
    private ILockService lockService;


    // 预置sql： INSERT INTO bpbizeventlistenings (ID,listensrcsucode, listensvc,svctype )
    //values('TMCM_AUTOPASSCHECKEXTENDEVENT','goldwind','com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskSettlementAutoDealController.settlementAutoDealQYFK','2')
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskSettlementAutoDealController.settlementAutoDealQYFK")
    public FsspResultRet settlementAutoDealQYFK(@RpcParam(paramName = "param") Map<String, Object> paramMap) {
        // 初始化日志编号
        logService.init("K1031");
        String billCode = "QYFKD"; // 企业付款单编码
        
        logService.info(billCode, "企业付款单复核前检查通过扩展入参：{}", JSON.toJSONString(paramMap));
        FsspResultRet ret = new FsspResultRet();
        ret.setResult(false);
        ret.setMessage(null);

        // 正式操作前加锁处理
        String jobId = "d2011779-3673-483b-a6bc-ca1032e0fbc8";
        String jobName = "jtgk企业付款单自动复核";
        String lockId;
        try {
            String moduleId = "JfskSettlementAutoDealController";
            String funcId = "settlementAutoDealQYFK";
            String categoryId = "String";
            String dataID = "jtgk-settlement-auto-deal-service";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                logService.error(billCode, jobName + "加锁失败");
                logService.flush();
                return ret;
            }
            lockId = lockResult.getLockId();
            logService.info(billCode, jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发送异常：" + ex.toString());
            logService.error(billCode ,jobName + "加锁过程发生异常：" + ExceptionUtils.getStackTrace(ex));
            logService.flush();
            return ret;
        }

        try {
            if (Objects.isNull(paramMap.get("BILLID"))) {
                logService.fail(billCode, "复核前检查通过扩展入参缺少BILLID");
                ret.setResult(false);
                ret.setMessage("复核前检查通过扩展入参缺少BILLID");
                logService.flush();
                return ret;
            }

            if (Objects.isNull(paramMap.get("FORMTYPE"))) {
                logService.fail(billCode, "复核前检查通过扩展入参缺少FORMTYPE");
                ret.setResult(false);
                ret.setMessage("复核前检查通过扩展入参缺少FORMTYPE");
                logService.flush();
                return ret;
            }

            String billId = String.valueOf(paramMap.get("BILLID"));
            // 检查是否存在同一来源的重复付款单
            String selectSQL = "select ID,DOCNO,DOCSTATUS,SRCDOCID,SRCDOCNO,SRCBIZSYS,TXT01 from TMPAYMENTSETTLEMENT where SRCDOCID=(select SRCDOCID from TMPAYMENTSETTLEMENT where ID='" + billId + "') and DOCSTATUS <> '-1'";
            logService.debug(billCode, "查询SQL: {}", selectSQL);

            List<Map<String, Object>> fkdList = DBUtil.querySql(selectSQL);
            if (fkdList == null || fkdList.size() == 0) {
                logService.fail(billCode, "未查询到指定的付款结算单：id={}", billId);
                ret.setResult(false);
                ret.setMessage("未查询到指定的付款结算单：id=" + billId);
                logService.flush();
                return ret;
            } else if (fkdList.size() > 1) {
                String srcDocId = (String) fkdList.get(0).get("SRCDOCID");
                logService.fail(billCode, "同一来源业务单据存在多笔付款结算单：srcDocId={}", srcDocId);
                ret.setResult(false);
                ret.setMessage("同一来源业务单据存在多笔付款结算单：srcDocId=" + srcDocId);
                logService.flush();
                return ret;
            }

            logService.info(billCode, "待处理的付款结算单：{}", JSON.toJSONString(fkdList));
            String formType = String.valueOf(paramMap.get("FORMTYPE"));
            String srcBizSys = (String) fkdList.get(0).get("SRCBIZSYS");
            String docStatus = fkdList.get(0).get("DOCSTATUS").toString();
            String txt01 = (String) fkdList.get(0).get("TXT01");

//            // 企业付款单复核前有外部流程状态为2
//            if (!"2".equals(docStatus)) {
//                logService.fail(billCode, "付款结算单单据状态不正确：id={}, docStatus={}", billId, docStatus);
//                ret.setResult(false);
//                ret.setMessage("付款结算单单据状态不正确：id=" + billId + ", docStatus=" + docStatus);
//                return ret;
//            }

            if ("TM_QYFKD".equals(formType)) {

                // 只有财司和云信的才可以自动复核
                String sql = "select TMPAYMENTSETTLEMENT.ID from TMPAYMENTSETTLEMENT\n" +
                        "inner join BPBIZPAYTREQPAYTEXECTRECD on TMPAYMENTSETTLEMENT.ID = BPBIZPAYTREQPAYTEXECTRECD.EXECTDOCID\n" +
                        "inner join BPBizPaymentReqReceiver on BPBizPaymentReqReceiver.ID = BPBIZPAYTREQPAYTEXECTRECD.BIZPAYMENTREQRECEIVERID\n" +
                        "inner join BPBizPaymentRequest on BPBizPaymentReqReceiver.parentID = BPBizPaymentRequest.ID\n" +
                        "inner join BFSettlementWay on BPBizPaymentReqReceiver.ExpSettleWay = BFSettlementWay.ID\n" +
                        "inner join JTGKPAYMENTDETAIL on JTGKPAYMENTDETAIL.requestDocId=BPBizPaymentRequest.ID\n" +
                        "inner join JTGKPAYMENTINFO on JTGKPAYMENTINFO.ID=JTGKPAYMENTDETAIL.parentID\n" +
                        "where  BFSettlementWay.Code in ('01', '04')\n" +
                        "and TMPAYMENTSETTLEMENT.ISBANKCOMMPAY ='1'\n" +
                        "and TMPAYMENTSETTLEMENT.id ='" + billId + "'";
//                        "where  BFSettlementWay.Code in ('01', '04')\n" +
//                        "and (JTGKPAYMENTINFO.srcPayMethod like '%财司%' or JTGKPAYMENTINFO.srcPayMethod like '%云信%') and TMPAYMENTSETTLEMENT.id ='" + billId + "'


                logService.info(billCode, "执行查询SQL：{}", sql);
                List<Map<String, Object>> mapOfBills = DBUtil.querySql(sql);

                if (mapOfBills == null || mapOfBills.isEmpty()) {
                    logService.info(billCode, "不符合自动复核条件，不进行自动复核");
                    logService.flush();
                    return ret;
                }else{
                    logService.info(billCode,"自动支付的企业付款单：" + billId);
                    ret.setResult(true);
                    ret.setMessage(null);
                    logService.flush();
                    return ret;
                }

            } else if ("TM_DWDBD".equals(formType)) {
                // 只有财司和云信的才可以自动复核
                String sql = "select TMPAYMENTSETTLEMENT.ID from TMPAYMENTSETTLEMENT \n" +
                        "inner join JTGKPAYMENTINFO on TMPAYMENTSETTLEMENT.SRCDOCID=JTGKPAYMENTINFO.ID\n" +
                        "inner join BFSettlementWay on JTGKPAYMENTINFO.SETTLEMENTWAYID = BFSettlementWay.ID\n" +
                        "where  BFSettlementWay.Code in ('01', '04')\n" +
                        "and TMPAYMENTSETTLEMENT.ISBANKCOMMPAY ='1'\n" +
                        "and TMPAYMENTSETTLEMENT.id ='" + billId + "'";

//                        "where  BFSettlementWay.Code in ('01', '04')\n" +
//                        "and (JTGKPAYMENTINFO.srcPayMethod like '%财司%' or JTGKPAYMENTINFO.srcPayMethod like '%云信%') and TMPAYMENTSETTLEMENT.id ='" + billId + "'";

                logService.info(billCode, "执行查询SQL：{}", sql);
                List<Map<String, Object>> mapOfBills = DBUtil.querySql(sql);

                if (mapOfBills == null || mapOfBills.isEmpty()) {
                    logService.info(billCode, "不符合自动复核条件，不进行自动复核");
                    logService.flush();
                    return ret;
                }else{
                    logService.info(billCode,"自动支付单位调拨单：" + billId);
                    ret.setResult(true);
                    ret.setMessage(null);
                    logService.flush();
                    return ret;
                }
            } else {
                // 非企业付款单不允许自动复核
                logService.warn(billCode, "不支持自动复核通过的付款结算单类型：{}", formType);
                ret.setResult(false);
                ret.setMessage(null);
                logService.flush();
                return ret;
            }
        } catch (Throwable ex) {
            logService.error(billCode, ex);
            ret.setResult(false);
            ret.setMessage("企业付款单复核前检查通过扩展执行出错：" + ex.toString());
            logService.flush();
            return ret;
        } finally {
            // 刷新缓存
            logService.flush();
            // 解锁lock
            try {
                lockService.removeLock(lockId);
                logService.info(billCode, jobName + "已解锁");
            } catch (Throwable ex) {
                SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
                logService.error(billCode, jobName + "解锁过程发生异常：" + ExceptionUtils.getStackTrace(ex));
            }
        }

    }

    // 预置sql： INSERT INTO bpbizeventlistenings (ID,listensrcsucode, listensvc,svctype )
    //values('TMCM_JSFHAUTOPASSEXTENDEVENT','goldwind','com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskSettlementAutoDealController.settlementAutoDealDWDB','2')
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskSettlementAutoDealController.settlementAutoDealDWDB")
    public FsspResultRet settlementAutoDealDWDB(@RpcParam Map<String, Object> paramMap) {
        // 初始化日志编号
        logService.init("K1031");
        String billCode = "DWDBD"; // 单位调拨单编码
        
        logService.info(billCode, "单位调拨单复核前检查通过扩展入参：{}", JSON.toJSONString(paramMap));
        FsspResultRet ret = new FsspResultRet();
        ret.setResult(false);
        ret.setMessage(null);

        // 正式操作前加锁处理
        String jobId = "5dd21c03-e36c-4ff9-9b37-baf75b894c6f";
        String jobName = "jtgk单位调拨单自动复核";
        String lockId;
        try {
            String moduleId = "JfskSettlementAutoDealController";
            String funcId = "settlementAutoDealDWDB";
            String categoryId = "String";
            String dataID = "jtgk-settlement-auto-deal-service";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                logService.error(billCode, jobName + "加锁失败");
                logService.flush();
                return ret;
            }
            lockId = lockResult.getLockId();
            logService.info(billCode, jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发送异常：" + ex.toString());
            logService.error(billCode ,jobName + "加锁过程发生异常：" + ExceptionUtils.getStackTrace(ex));
            logService.flush();
            return ret;
        }


        try {
            if (Objects.isNull(paramMap.get("BILLID"))) {
                logService.fail(billCode, "复核前检查通过扩展入参缺少BILLID");
                ret.setResult(false);
                ret.setMessage("复核前检查通过扩展入参缺少BILLID");
                logService.flush();
                return ret;
            }
            
            if (Objects.isNull(paramMap.get("FORMTYPE"))) {
                logService.fail(billCode, "复核前检查通过扩展入参缺少FORMTYPE");
                ret.setResult(false);
                ret.setMessage("复核前检查通过扩展入参缺少FORMTYPE");
                logService.flush();
                return ret;
            }
            
            String billId = String.valueOf(paramMap.get("BILLID"));
            // 检查是否存在同一来源的重复付款单
            String selectSQL = "select ID,DOCNO,DOCSTATUS,SRCDOCID,SRCDOCNO,SRCBIZSYS,TXT01 from TMPAYMENTSETTLEMENT where SRCDOCID=(select SRCDOCID from TMPAYMENTSETTLEMENT where ID='" + billId + "') and DOCSTATUS <> '-1'";
            logService.debug(billCode, "查询SQL: {}", selectSQL);
            
            List<Map<String, Object>> fkdList = DBUtil.querySql(selectSQL);
            if (fkdList == null || fkdList.size() == 0) {
                logService.fail(billCode, "未查询到指定的付款结算单：id={}", billId);
                ret.setResult(false);
                ret.setMessage("未查询到指定的付款结算单：id=" + billId);
                logService.flush();
                return ret;
            } else if (fkdList.size() > 1) {
                String srcDocId = (String) fkdList.get(0).get("SRCDOCID");
                logService.fail(billCode, "同一来源业务单据存在多笔付款结算单：srcDocId={}", srcDocId);
                ret.setResult(false);
                ret.setMessage("同一来源业务单据存在多笔付款结算单：srcDocId=" + srcDocId);
                logService.flush();
                return ret;
            }
            
            logService.info(billCode, "待处理的付款结算单：{}", JSON.toJSONString(fkdList));
            String formType = String.valueOf(paramMap.get("FORMTYPE"));
            String srcBizSys = (String) fkdList.get(0).get("SRCBIZSYS");
            String docStatus = fkdList.get(0).get("DOCSTATUS").toString();
            String txt01 = (String) fkdList.get(0).get("TXT01");
            
//            // 单位调拨无外部流程复核前状态为2
//            if (!"2".equals(docStatus)) {
//                logService.fail(billCode, "付款结算单单据状态不正确：id={}, docStatus={}", billId, docStatus);
//                ret.setResult(false);
//                ret.setMessage("付款结算单单据状态不正确：id=" + billId + ", docStatus=" + docStatus);
//                return ret;
//            }
            
            if ("TM_DWDBD".equals(formType)) {

                // 只有财司和云信的才可以自动复核
                String sql = "select TMPAYMENTSETTLEMENT.ID from TMPAYMENTSETTLEMENT \n" +
                        "inner join JTGKPAYMENTINFO on TMPAYMENTSETTLEMENT.SRCDOCID=JTGKPAYMENTINFO.ID\n" +
                        "inner join BFSettlementWay on JTGKPAYMENTINFO.SETTLEMENTWAYID = BFSettlementWay.ID\n" +
                        "where  BFSettlementWay.Code in ('01', '04')\n" +
                        "and TMPAYMENTSETTLEMENT.ISBANKCOMMPAY ='1'\n" +
                        "and TMPAYMENTSETTLEMENT.id ='" + billId + "'";

//                        "where  BFSettlementWay.Code in ('01', '04')\n" +
//                        "and (JTGKPAYMENTINFO.srcPayMethod like '%财司%' or JTGKPAYMENTINFO.srcPayMethod like '%云信%') and TMPAYMENTSETTLEMENT.id ='" + billId + "'";

                logService.info(billCode, "执行查询SQL：{}", sql);
                List<Map<String, Object>> mapOfBills = DBUtil.querySql(sql);

                if (mapOfBills == null || mapOfBills.isEmpty()) {
                    logService.info(billCode, "不符合自动复核条件，不进行自动复核");
                    logService.flush();
                    return ret;
                }else{
                    logService.info(billCode,"自动支付单位调拨单：" + billId);
                    ret.setResult(true);
                    ret.setMessage(null);
                    logService.flush();
                    return ret;
                }

            } else {
                // 非单位调拨单不允许自动复核
                logService.warn(billCode, "不支持自动复核通过的付款结算单类型：{}", formType);
                ret.setResult(false);
                ret.setMessage(null);
                logService.flush();
                return ret;
            }
        } catch (Throwable ex) {
            logService.error(billCode, ex);
            ret.setResult(false);
            ret.setMessage("单位调拨单复核前检查通过扩展执行出错：" + ex.toString());
            logService.flush();
            return ret;
        } finally {
            // 刷新缓存
            logService.flush();
            // 解锁lock
            try {
                lockService.removeLock(lockId);
                logService.info(billCode, jobName + "已解锁");
            } catch (Throwable ex) {
                SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
                logService.error(billCode, jobName + "解锁过程发生异常：" + ExceptionUtils.getStackTrace(ex));
            }
        }
    }
}
