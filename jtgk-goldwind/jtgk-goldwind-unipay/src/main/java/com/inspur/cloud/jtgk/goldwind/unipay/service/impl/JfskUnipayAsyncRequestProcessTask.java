package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.config.JfskUnipayConst;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.*;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.*;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPaymentDetailRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPaymentInfoRepository;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskDefaultPayAccountService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskProfitCenterService;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest.JfskBizPayRequestService;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement.JfskAllocationBillService;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement.JfskAllocationBillServiceWrapper;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.TransactionCoordinator;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.lockservice.api.ILockService;
import io.netty.util.internal.StringUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;

/**
 * 待付池付款申请处理任务：由线程池创建并执行
 */
@Slf4j
public class JfskUnipayAsyncRequestProcessTask implements Callable<PaymentResultDto> {
    private final JfskPaymentInfoRepository paymentInfoRepository;
    private final JfskPaymentDetailRepository paymentDetailRepository;
    private final JfskDefaultPayAccountService defaultPayAccountService;
    private final JfskProfitCenterService profitCenterService;
    private final JfskBizPayRequestServiceWrapper bizPayRequestServiceWrapper;
    private final JfskBizPayRequestService bizPayRequestService;
    private final JfskAllocationBillService allocationBillService;
    private final JfskAllocationBillServiceWrapper allocationBillServiceWrapper;
    private final LogService logService;
    private final PaymentInfoDto request;
    
    // 任务ID，用于事务协调器中识别任务
    @Getter
    private final String taskId;
    
    // 事务协调器，用于外部管理事务
    private TransactionCoordinator transactionCoordinator;

    /**
     * 业务系统发起付款申请写入代付池并生成业务支付申请或单位调拨单
     * @param request 付款申请信息
     */
    public JfskUnipayAsyncRequestProcessTask(
            LogService logService,
            JfskPaymentInfoRepository paymentInfoRepository,
            JfskPaymentDetailRepository paymentDetailRepository,
            JfskDefaultPayAccountService defaultPayAccountService,
            JfskProfitCenterService profitCenterService,
            JfskBizPayRequestService bizPayRequestService,
            JfskBizPayRequestServiceWrapper bizPayRequestServiceWrapper,
            JfskAllocationBillService allocationBillService,
            JfskAllocationBillServiceWrapper allocationBillServiceWrapper,
            PaymentInfoDto request) {
        this.logService = logService;
        this.paymentInfoRepository = paymentInfoRepository;
        this.paymentDetailRepository = paymentDetailRepository;
        this.defaultPayAccountService = defaultPayAccountService;
        this.profitCenterService = profitCenterService;
        this.bizPayRequestService = bizPayRequestService;
        this.bizPayRequestServiceWrapper = bizPayRequestServiceWrapper;
        this.allocationBillService = allocationBillService;
        this.allocationBillServiceWrapper = allocationBillServiceWrapper;
        this.request = request;
        // 使用源系统标识+单据ID作为任务唯一标识
        this.taskId = request.getSrcBizSys() + "-" + request.getSrcDocId();
    }
    
    /**
     * 设置事务协调器
     * @param coordinator 事务协调器
     */
    public void setTransactionCoordinator(TransactionCoordinator coordinator) {
        this.transactionCoordinator = coordinator;
    }

    /**
     * 业务系统发起付款申请写入代付池并生成业务支付申请或单位调拨单
     * 此方法支持两种事务模式：
     * 1. 单独事务模式 - 每个任务独立的事务（无事务协调器时）
     * 2. 协调事务模式 - 多个任务独立事务但协调提交/回滚（设置了事务协调器时）
     * @return 是否接收成功
     */
    public PaymentResultDto call() {
        logService.init("K0101");
        log.info("准备处理付款申请：" + JSON.toJSONString(request));
        logService.info(request.getSrcDocNo(), "准备处理付款申请：" + JSON.toJSONString(request));
        PaymentResultDto resultDto = new PaymentResultDto(request.getSrcBizSys(), request.getSrcDocType(), request.getSrcDocId(), false, null);

        // 更新任务状态为运行中（如果使用事务协调器）
        if (transactionCoordinator != null) {
            log.info("任务使用事务协调器: {}", taskId);
            transactionCoordinator.setTaskRunning(taskId);
        } else {
            log.info("任务未使用事务协调器: {}", taskId);
        }
        
        JfskPaymentInfoEntity logEntity = createLogEntity(request);
        log.info("准备保存接口日志表：" + JSON.toJSONString(logEntity));
        logService.info(request.getSrcDocNo(), "准备保存接口日志表：id=" + logEntity.getId() + ", docStatus=" + logEntity.getDocStatus());
        paymentInfoRepository.save(logEntity);
        log.info("接口日志表保存完成");
        logService.info(request.getSrcDocNo(), "接口日志表保存完成");
        R checkResult = null;
        try{
            checkResult = check(logService, request, logEntity, transactionCoordinator, taskId);
        }catch (Throwable e){
            checkResult = R.error(e.getMessage());
            log.error(e.getMessage());
            logService.info(request.getSrcDocNo(), ExceptionUtils.getStackTrace(e));
        }
        paymentInfoRepository.save(logEntity);
        log.info("接口日志表(处理后)保存完成");
        if (!checkResult.getResult()) {
            log.error("入参校验失败：" + checkResult.getMessage());
            logService.error(request.getSrcDocNo(), "入参校验失败：" + checkResult.getMessage());
            String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=-1,MESSAGE=?1 where DOCSTATUS=0 and ID='" + logEntity.getId() + "'";
            log.info("准备保存接口日志表：" + updateSql + ", ?1=" + checkResult.getMessage());
            logService.info(request.getSrcDocNo(), "准备保存接口日志表：" + updateSql + ", ?1=" + checkResult.getMessage());
            int count = DBUtil.executeUpdateSQL(updateSql, checkResult.getMessage());
            log.info("接口日志表保存完成：" + count);
            logService.info(request.getSrcDocNo(), "接口日志表保存完成：" + count);
            resultDto.setMessage(checkResult.getMessage());
            
            // 如果使用事务协调器，设置任务失败
            if (transactionCoordinator != null) {
                log.info("设置事务协调器任务失败状态：taskId={}, message={}", taskId, checkResult.getMessage());
                logService.info(request.getSrcDocNo(), "设置事务协调器任务失败状态：taskId=" + taskId + ", message=" + checkResult.getMessage());
                transactionCoordinator.setTaskFailed(taskId, checkResult.getMessage(), null);
            } else {
                // 如果没有使用事务协调器，则需要回滚当前事务
                log.info("未使用事务协调器，回滚当前事务");
                logService.info(request.getSrcDocNo(), "未使用事务协调器，回滚当前事务");
                try {
                    JpaTransaction.getTransaction().rollback();
                    log.info("当前事务已回滚");
                    logService.info(request.getSrcDocNo(), "当前事务已回滚");
                } catch (Exception ex) {
                    log.error("回滚事务失败：", ex);
                    logService.error(request.getSrcDocNo(), "回滚事务失败：" + ExceptionUtils.getStackTrace(ex));
                }
            }
            
            logService.flush();
            return resultDto;
        }
        if ("0".equals(logEntity.getDirectPay())) {
            log.info("需要资金排程付款安排时只保存到待付池");
            logService.info(request.getSrcDocNo(), "需要资金排程付款安排时只保存到待付池");
            String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=1,MESSAGE=null where DOCSTATUS=0 and ID='" + logEntity.getId() + "'";
            log.info("准备保存接口日志表：" + updateSql);
            logService.info(request.getSrcDocNo(), "准备保存接口日志表：" + updateSql);
            int count = DBUtil.executeUpdateSQL(updateSql);
            log.info("接口日志表保存完成：" + count);
            logService.info(request.getSrcDocNo(), "接口日志表保存完成：" + count);
            resultDto.setResult(true);
            resultDto.setMessage("付款申请已存入待付池");
            
            // 如果使用事务协调器，设置任务完成
            if (transactionCoordinator != null) {
                transactionCoordinator.setTaskCompleted(taskId);
            }
            
            logService.flush();
            return resultDto;
        }else if ("FSSC".equals(request.getSrcBizSys()) && "9".equals(request.getExtPayMethod())) {
            // 共享 + 9 （承兑+现汇付款） 停留在待付池待拆分
            log.info("共享 + 9 （承兑+现汇付款） 停留在待付池待拆分 时只保存到待付池");
            logService.info(request.getSrcDocNo(), "共享 + 9 （承兑+现汇付款） 停留在待付池待拆分  只保存到待付池");
            String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=1,MESSAGE=null where DOCSTATUS=0 and ID='" + logEntity.getId() + "'";
            log.info("准备保存接口日志表：" + updateSql);
            logService.info(request.getSrcDocNo(), "准备保存接口日志表：" + updateSql);
            int count = DBUtil.executeUpdateSQL(updateSql);
            log.info("接口日志表保存完成：" + count);
            logService.info(request.getSrcDocNo(), "接口日志表保存完成：" + count);
            resultDto.setResult(true);
            resultDto.setMessage("付款申请已存入待付池");
            
            // 如果使用事务协调器，设置任务完成
            if (transactionCoordinator != null) {
                transactionCoordinator.setTaskCompleted(taskId);
            }
            
            logService.flush();
            return resultDto;
        }

        if ("FSSC".equals(request.getSrcBizSys()) && !StringUtil.isNullOrEmpty(request.getRefSrcDocId())) {
            cancelRefSrcDocId(logService, request);
        }

        // 创建每个线程自己的事务
        JpaTransaction transaction = JpaTransaction.getTransaction();
        
        try {
            // 开始事务
            transaction.begin();
            log.info("线程本地事务已启动: {}", taskId);
            logService.info(request.getSrcDocNo(), "线程本地事务已启动: " + taskId);
            
            GenerateResultDto generateResult;
            String payDocType = "0";
            log.info("单位调拨判断：srcBizSys=" + request.getSrcBizSys() + ", srcDocType=" + request.getSrcDocType() + ", payAccountUnit=" + logEntity.getPayAccountOpenUnitId() +
                    ", oppAccountUnit=" + logEntity.getReceivingBankAccountOpenUnitId());
            if ("FSSC".equals(request.getSrcBizSys()) && request.getSrcDocType().startsWith("资金调拨同名")) {
                if (!logEntity.getPayAccountOpenUnitId().equals(logEntity.getReceivingBankAccountOpenUnitId())){
                    resultDto.setMessage("收付银行账号的开户单位不同，无法进行资金调拨");

                    // 如果使用事务协调器，设置任务失败
                    if (transactionCoordinator != null) {
                        log.info("设置事务协调器任务失败状态：taskId={}, message={}", taskId, "收付银行账号的开户单位不同，无法进行资金调拨");
                        logService.info(request.getSrcDocNo(), "设置事务协调器任务失败状态：taskId=" + taskId + ", message=收付银行账号的开户单位不同，无法进行资金调拨");
                        transactionCoordinator.setTaskFailed(taskId, "收付银行账号的开户单位不同，无法进行资金调拨", new JfskException("收付银行账号的开户单位不同，无法进行资金调拨"));
                    }

                    // 回滚本地事务
                    transaction.rollback();
                    log.info("线程本地事务已回滚: {}", taskId);
                    logService.info(request.getSrcDocNo(), "线程本地事务已回滚: " + taskId);

                    // 在新事务中更新接口日志表状态为失败
                    log.info("开户单位不同失败时更新接口日志表：" + "收付银行账号的开户单位不同，无法进行资金调拨");
                    JpaTransaction transactionOne = JpaTransaction.getTransaction();
                    transactionOne.begin();
                    try {
                        // 更新支付信息为失败状态
                        logEntity.setDocStatus(-1);
                        logEntity.setMessage("收付银行账号的开户单位不同，无法进行资金调拨");
                        paymentInfoRepository.save(logEntity);
                        log.info("更新接口日志表完成");
                        logService.info(request.getSrcDocNo(), "更新接口日志表完成");
                        transactionOne.commit();
                    } catch (Exception e) {
                        log.error("开户单位不同失败时更新接口日志表失败：" + e.getMessage());
                        logService.error(request.getSrcDocNo(), "开户单位不同失败时更新接口日志表失败：" + e.getMessage());
                        transactionOne.rollback();
                    }

                    return resultDto;
                }
                log.info("准备生成单位调拨单(共享发起的同名户资金调拨)");
                logService.info(request.getSrcDocNo(), "准备生成单位调拨单(共享发起的同名户资金调拨)");
                payDocType = "5";
                
                // 检查是否有其他任务已失败（如果使用事务协调器）
                if (transactionCoordinator != null && transactionCoordinator.hasTaskFailed()) {
                    log.info("由于存在其他任务失败，跳过实际单位调拨单生成");
                    logService.info(request.getSrcDocNo(), "由于存在其他任务失败，跳过实际单位调拨单生成");
                    generateResult = new GenerateResultDto();
                    generateResult.setResult(false);
                    generateResult.setMessage("由于其他任务失败，此操作已取消");
                } else {
                    // 再次检查是否有任务失败（双重检查）
                    if (transactionCoordinator != null) {
                        if ((bizPayRequestServiceWrapper != null && bizPayRequestServiceWrapper.isTransactionFailed()) || 
                            (allocationBillServiceWrapper != null && allocationBillServiceWrapper.isTransactionFailed())) {
                            log.info("检测到事务已失败的标记，跳过单位调拨单生成");
                            logService.info(request.getSrcDocNo(), "检测到事务已失败的标记，跳过单位调拨单生成");
                            generateResult = new GenerateResultDto();
                            generateResult.setResult(false);
                            generateResult.setMessage("由于事务已失败，此操作已取消");
                        } else {
                            // 直接调用服务，不使用包装器
                            String srcPayMethodCode = logEntity.getSrcPayMethodCode();
                            if ("5".equals(srcPayMethodCode) || "1".equals(srcPayMethodCode)) {
                                generateResult = allocationBillService.generateNew(logService, logEntity);
                            } else {
                                generateResult = allocationBillService.generate(logService, logEntity);
                            }
                            
                            // 如果生成失败，标记任务失败
                            if (!generateResult.getResult()) {
                                log.warn("单位调拨单生成失败，标记任务失败");
                                transactionCoordinator.setTaskFailed(taskId, generateResult.getMessage(), new JfskException(generateResult.getMessage()));
                                // 同时也标记事务服务为失败状态
                                if (allocationBillServiceWrapper != null) {
                                    allocationBillServiceWrapper.markTransactionFailed();
                                }
                                if (bizPayRequestServiceWrapper != null) {
                                    bizPayRequestServiceWrapper.markTransactionFailed();
                                }
                            }
                        }
                    } else {
                        // 未使用事务协调器，直接调用原始服务
                        String srcPayMethodCode = logEntity.getSrcPayMethodCode();
                        if ("5".equals(srcPayMethodCode) || "1".equals(srcPayMethodCode)) {
                            generateResult = allocationBillService.generateNew(logService, logEntity);
                        } else {
                            generateResult = allocationBillService.generate(logService, logEntity);
                        }
                    }
                }
                
                log.info("单位调拨单生单结果：" + JSON.toJSONString(generateResult));
                logService.info(request.getSrcDocNo(), "单位调拨单生单结果：result=" + generateResult.getResult() + ", docId=" + generateResult.getDocId());
            } else {
                log.info("准备生成业务支付申请");
                logService.info(request.getSrcDocNo(), "准备生成业务支付申请");
                
                // 检查是否有其他任务已失败（如果使用事务协调器）
                if (transactionCoordinator != null && transactionCoordinator.hasTaskFailed()) {
                    log.info("检测到其他任务已经失败，跳过RPC调用，直接返回失败结果");
                    logService.info(request.getSrcDocNo(), "检测到其他任务已经失败，跳过RPC调用，直接返回失败结果");
                    
                    // 创建一个失败的结果对象
                    generateResult = new GenerateResultDto();
                    generateResult.setResult(false);
                    generateResult.setMessage("由于其他任务已失败，为确保数据一致性，此任务已取消");
                } else {
                    // 再次检查allocationBillServiceWrapper是否已标记事务失败
                    if (transactionCoordinator != null && allocationBillServiceWrapper != null && allocationBillServiceWrapper.isTransactionFailed()) {
                        log.info("检测到单位调拨单服务已标记事务失败，跳过业务支付申请生成");
                        logService.info(request.getSrcDocNo(), "检测到单位调拨单服务已标记事务失败，跳过业务支付申请生成");
                        generateResult = new GenerateResultDto();
                        generateResult.setResult(false);
                        generateResult.setMessage("由于单位调拨单服务失败，此操作已取消");
                    } else {
                        // 直接调用服务，不使用包装器
                        generateResult = bizPayRequestService.generate(logService, logEntity);
                        
                        // 如果生成失败且使用了事务协调器，标记任务失败
                        if (!generateResult.getResult() && transactionCoordinator != null) {
                            log.warn("业务支付申请生成失败，标记任务失败");
                            transactionCoordinator.setTaskFailed(taskId, generateResult.getMessage(), new JfskException(generateResult.getMessage()));
                            // 同时也标记事务服务为失败状态
                            if (bizPayRequestServiceWrapper != null) {
                                bizPayRequestServiceWrapper.markTransactionFailed();
                            }
                            // 同步标记单位调拨单服务失败，确保它们状态一致
                            if (allocationBillServiceWrapper != null) {
                                allocationBillServiceWrapper.markTransactionFailed();
                            }
                        }
                    }
                }
                
                log.info("业务支付申请生单结果：" + JSON.toJSONString(generateResult));
                logService.info(request.getSrcDocNo(), "业务支付申请生单结果：result=" + generateResult.getResult() + ", docNo=" + generateResult.getDocNo());
            }

            boolean updateLogEntityMessage = false;

            if (generateResult.getResult()) {
                String docId = generateResult.getDocId();
                String docNo = generateResult.getDocNo();
                log.info("生单成功后更新待付池状态");

                // 更新支付信息
                logEntity.setDocStatus(2);
                logEntity.setMessage(null);
                paymentInfoRepository.save(logEntity);
                log.info("更新接口日志表完成");
                logService.info(request.getSrcDocNo(), "更新接口日志表完成");
                
                // 创建明细实体
                JfskPaymentDetailEntity detailEntity = createDetailEntity(logEntity, payDocType, docId, docNo);
                log.info("准备保存执行记录表：" + JSON.toJSONString(detailEntity));
                logService.info(request.getSrcDocNo(), "准备保存执行记录表：id=" + detailEntity.getId());
                
                // 保存明细实体
                paymentDetailRepository.save(detailEntity);
                log.info("执行记录表保存完成");
                logService.info(request.getSrcDocNo(), "执行记录表保存完成");
                
                // 设置结果
                resultDto.setResult(true);
                resultDto.setMessage("付款申请已生成付款单据：" + docNo);

                // 如果使用事务协调器，标记任务成功完成
                if (transactionCoordinator != null) {
                    transactionCoordinator.setTaskCompleted(taskId);
                }
            } else {
                log.info("生单失败后处理");

                updateLogEntityMessage = true;
                
                resultDto.setResult(false);
                resultDto.setMessage(generateResult.getMessage());
                
                // 如果使用事务协调器，报告任务失败
                if (transactionCoordinator != null) {
                    String errorMsg = "生单失败：" + generateResult.getMessage();
                    transactionCoordinator.setTaskFailed(taskId, errorMsg, new JfskException(errorMsg));
                }
            }
            
            // 如果使用事务协调器，等待所有任务完成业务逻辑，并根据全局状态决定提交或回滚
            if (transactionCoordinator != null) {
                log.info("等待所有线程完成业务逻辑执行并协调事务处理: {}", taskId);
                logService.info(request.getSrcDocNo(), "等待所有线程完成业务逻辑执行并协调事务处理: " + taskId);
                
                boolean allSuccess = transactionCoordinator.waitForAllTasksExecution();
                
                if (allSuccess) {
                    log.info("所有任务执行成功，提交本地事务: {}", taskId);
                    logService.info(request.getSrcDocNo(), "所有任务执行成功，提交本地事务: " + taskId);
                    transaction.commit();
                    log.info("线程本地事务已提交: {}", taskId);
                    logService.info(request.getSrcDocNo(), "线程本地事务已提交: " + taskId);
                } else {
                    log.info("存在失败任务，回滚本地事务: {}", taskId);
                    logService.info(request.getSrcDocNo(), "存在失败任务，回滚本地事务: " + taskId);
                    transaction.rollback();
                    log.info("线程本地事务已回滚: {}", taskId);
                    logService.info(request.getSrcDocNo(), "线程本地事务已回滚: " + taskId);

                    if (updateLogEntityMessage) {
                        log.info("回滚时更新接口日志表：" + generateResult.getMessage());
                        JpaTransaction transactionOne = JpaTransaction.getTransaction();
                        transactionOne.begin();
                        try {
                            // 更新支付信息
                            logEntity.setDocStatus(-1);
                            logEntity.setMessage(generateResult.getMessage());
                            paymentInfoRepository.save(logEntity);
                            log.info("更新接口日志表完成");
                            logService.info(request.getSrcDocNo(), "更新接口日志表完成");
                            transactionOne.commit();
                        } catch (Exception e) {
                            log.error("回滚时更新接口日志表失败：" + e.getMessage());
                            logService.error(request.getSrcDocNo(), "回滚时更新接口日志表失败：" + e.getMessage());
                            transactionOne.rollback();
                        } 
                    }

                    // 如果当前任务本身是成功的，但由于其他任务失败而回滚，更新结果
                    if (resultDto.getResult()) {
                        resultDto.setResult(false);
                        resultDto.setMessage("由于其他任务失败，操作已回滚");
                    }
                }
                
                // 确保清理包装器中的事务状态
                if (bizPayRequestServiceWrapper != null) {
                    bizPayRequestServiceWrapper.clearTransactionFailedMark();
                }
                if (allocationBillServiceWrapper != null) {
                    allocationBillServiceWrapper.clearTransactionFailedMark();
                }
            } else {
                // 如果不使用事务协调器，直接提交事务
                transaction.commit();
                log.info("线程本地事务已提交: {}", taskId);
                logService.info(request.getSrcDocNo(), "线程本地事务已提交: " + taskId);
            }
        } catch (Throwable ex) {
            // 发生异常时回滚事务
            try {
                transaction.rollback();
                log.error("线程本地事务已回滚: {}", taskId, ex);
                logService.error(request.getSrcDocNo(), "线程本地事务已回滚: " + taskId + ", 异常: " + ExceptionUtils.getStackTrace(ex));
            } catch (Exception rollbackEx) {
                log.error("回滚线程本地事务失败: {}", taskId, rollbackEx);
                logService.error(request.getSrcDocNo(), "回滚线程本地事务失败: " + taskId + ", 异常: " + ExceptionUtils.getStackTrace(rollbackEx));
            }
            
            // 如果使用事务协调器，报告任务失败
            if (transactionCoordinator != null) {
                transactionCoordinator.setTaskFailed(taskId, "执行过程中发生异常：" + ex.getMessage(), ex);
                
                // 发生异常时，同时标记服务失败
                if (bizPayRequestServiceWrapper != null) {
                    bizPayRequestServiceWrapper.markTransactionFailed();
                }
                if (allocationBillServiceWrapper != null) {
                    allocationBillServiceWrapper.markTransactionFailed();
                }
            }
            
            // 设置返回结果为失败
            resultDto.setResult(false);
            resultDto.setMessage("处理过程中发生异常：" + ex.getMessage());
        }
        
        logService.flush();
        return resultDto;
    }

    /**
     * 共享系统组合付款拆分后推送时，作废之前的组合支付记录
     * @param logService 接口日志
     * @param requestDto 付款申请信息
     */
    private void cancelRefSrcDocId(LogService logService, PaymentInfoDto requestDto) {
        log.info("准备作废关联的组合支付记录：refSrcDocId=" + requestDto.getRefSrcDocId());
        logService.info(requestDto.getSrcDocNo(), "准备作废关联的组合支付记录：refSrcDocId=" + requestDto.getRefSrcDocId());
        String updateSql = "update JTGKPAYMENTINFO set DOCSTATUS=-3 where DOCSTATUS=1" +
                " and SRCBIZSYS='" + requestDto.getSrcBizSys() + "'" +
                (StringUtil.isNullOrEmpty(requestDto.getSrcDocType()) ? "" : (" and SRCDOCTYPE='" + requestDto.getSrcDocType() + "'")) +
                " and SRCDOCID='" + requestDto.getRefSrcDocId() + "'";
        log.info(updateSql);
        logService.info(requestDto.getSrcDocNo(), updateSql);
        int count = DBUtil.executeUpdateSQL(updateSql);
        log.info("受影响行数：" + count);
        logService.info(requestDto.getSrcDocNo(), "受影响行数：" + count);
    }

    /**
     * 检查请求入参并生成接口日志数据
     * @param logService 接口日志
     * @param requestDto 请求入参
     * @param logEntity 接口日志数据
     * @param transactionCoordinator 事务协调器，可以为null
     * @param taskId 任务ID，可以为null
     * @return 请求入参是否有效
     */
    private R check(LogService logService, PaymentInfoDto requestDto, JfskPaymentInfoEntity logEntity, 
                   TransactionCoordinator transactionCoordinator, String taskId) {
        log.info("准备检查来源系统标识：" + requestDto.getSrcBizSys());
        if (StringUtils.isBlank(requestDto.getSrcBizSys())) {
            log.error("来源系统标识不能为空");
            logService.error(requestDto.getSrcDocNo(), "来源系统标识不能为空");
            return R.error("来源系统标识不能为空");
        }
        String selectBizSys = "select CODE,NAME from IDD_DATADICTIONARY where CATEGORYID='25c76693-a046-de5a-c474-49a8af20f2a8' and CODE=?1";
        log.info(selectBizSys + ", ?1=" + requestDto.getSrcBizSys());
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectBizSys, requestDto.getSrcBizSys());
        log.info(JSON.toJSONString(rowsOfBizSys));
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            log.error("来源系统标识" + requestDto.getSrcBizSys() + "无效");
            logService.error(requestDto.getSrcDocNo(), "来源系统标识" + requestDto.getSrcBizSys() + "无效");
            return R.error("来源系统标识" + requestDto.getSrcBizSys() + "无效");
        }
        if (StringUtils.isBlank(requestDto.getSrcDocId())) {
            log.error("来源单据唯一ID不能为空");
            logService.error(requestDto.getSrcDocNo(), "来源单据唯一ID不能为空");
            return R.error("来源单据唯一ID不能为空");
        }
        if (StringUtils.isBlank(requestDto.getSrcDocNo())) {
            log.error("来源单据编号不能为空");
            logService.error(requestDto.getSrcDocNo(), "来源单据编号不能为空");
            return R.error("来源单据编号不能为空");
        }
        log.info("准备按来源系统+来源单据ID检查是否重复推送：srcBizSys=" + requestDto.getSrcBizSys() + ", srcDocId=" + requestDto.getSrcDocId());
        String selectExistsLogs = "select ID,SRCBIZSYS,SRCDOCTYPE,SRCDOCID,DOCSTATUS from JTGKPAYMENTINFO where DOCSTATUS not in (0,-1)" +
                " and SRCBIZSYS='" + requestDto.getSrcBizSys() + "'" +
                (StringUtil.isNullOrEmpty(requestDto.getSrcDocType()) ? "" : (" and SRCDOCTYPE='" + requestDto.getSrcDocType() + "'")) +
                " and SRCDOCID='" + requestDto.getSrcDocId() + "'";
        log.info(selectExistsLogs);
        List<Map<String, Object>> rowsOfExistsLogs = DBUtil.querySql(selectExistsLogs);
        log.info(JSON.toJSONString(rowsOfExistsLogs));
        if (rowsOfExistsLogs != null && !rowsOfExistsLogs.isEmpty()) {
            String errorMsg = "来源单据已接收不允许再次推送：srcBizSys=" + requestDto.getSrcBizSys() + ", srcDocId=" + requestDto.getSrcDocId();
            log.error(errorMsg);
            logService.error(requestDto.getSrcDocNo(), errorMsg);
            
            // 如果有事务协调器，直接在这里标记任务失败，确保事务会被回滚
            if (transactionCoordinator != null) {
                log.warn("检测到重复提交，直接标记任务失败，任务ID: {}", taskId);
                logService.warn(requestDto.getSrcDocNo(), "检测到重复提交，直接标记任务失败，任务ID: " + taskId);
                transactionCoordinator.setTaskFailed(taskId, "来源单据已接收不允许再次推送", 
                    new JfskException("来源单据已接收不允许再次推送"));
            }
            
            return R.error("来源单据已接收不允许再次推送");
        }
        if (!StringUtils.isBlank(requestDto.getExtDirectPay())) {
            if (!JfskUnipayConst.SFS.equals(requestDto.getSrcBizSys())) {
                log.error("来源系统" + requestDto.getSrcBizSys() + "不允许传入资金排程付款安排标识");
                logService.error(requestDto.getSrcDocNo(), "来源系统" + requestDto.getSrcBizSys() + "不允许传入资金排程付款安排标识");
                return R.error("来源系统" + requestDto.getSrcBizSys() + "不允许传入资金排程付款安排标识");
            }
            log.info("准备检查是否需要资金排程付款安排：" + requestDto.getExtDirectPay());
            if (!"0".equals(requestDto.getExtDirectPay()) && !"1".equals(requestDto.getExtDirectPay())) {
                log.error("是否需要资金排程付款安排无效：" + requestDto.getExtDirectPay());
                logService.error(requestDto.getSrcDocNo(), "是否需要资金排程付款安排无效：" + requestDto.getExtDirectPay());
                return R.error("是否需要资金排程付款安排无效");
            }
        } else {
            // 默认不需要付款安排
            logEntity.setDirectPay("1");
        }
        if ("1".equals(logEntity.getDirectPay())) {
            String selectBizRequest = "select ID,DOCNO,DOCSTATUS from BPBIZPAYMENTREQUEST where SRCBIZSYS=?1 and SRCDOCID=?2";
            log.info("准备检查业务支付申请是否已生成：" + selectBizRequest + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getSrcDocId());
            logService.info(requestDto.getSrcDocNo(), "准备检查业务支付申请是否已生成：" + selectBizRequest + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getSrcDocId());
            List<Map<String, Object>> rowsOfBizRequest = DBUtil.querySql(selectBizRequest, requestDto.getSrcBizSys(), requestDto.getSrcDocId());
            log.info(JSON.toJSONString(rowsOfBizRequest));
            if (rowsOfBizRequest != null && !rowsOfBizRequest.isEmpty()) {
                log.error("业务支付申请已生成：srcBizSys=" + requestDto.getSrcBizSys() + ", srcDocId=" + requestDto.getSrcDocId());
                logService.error(requestDto.getSrcDocNo(), "业务支付申请已生成：srcBizSys=" + requestDto.getSrcBizSys() + ", srcDocId=" + requestDto.getSrcDocId());
                return R.error("业务支付申请已生成");
            }
            String selectPayDocs = "select ID,DOCNO,DOCSTATUS from TMPAYMENTSETTLEMENT where SRCBIZSYS=?1 and SRCDOCID=?2";
            log.info("准备检查付款单是否已生成：" + selectPayDocs + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getSrcDocId());
            logService.info(requestDto.getSrcDocNo(), "准备检查业务支付申请是否已生成：" + selectPayDocs + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getSrcDocId());
            List<Map<String, Object>> rowsOfPayDocs = DBUtil.querySql(selectPayDocs, requestDto.getSrcBizSys(), requestDto.getSrcDocId());
            log.info(JSON.toJSONString(rowsOfPayDocs));
            if (rowsOfPayDocs != null && !rowsOfPayDocs.isEmpty()) {
                log.error("付款单已生成：srcBizSys=" + requestDto.getSrcBizSys() + ", srcDocId=" + requestDto.getSrcDocId());
                logService.error(requestDto.getSrcDocNo(), "付款单已生成：srcBizSys=" + requestDto.getSrcBizSys() + ", srcDocId=" + requestDto.getSrcDocId());
                return R.error("付款单已生成");
            }
        }
        log.info("准备检查付款公司：" + requestDto.getRequestUnitCode());
        if (StringUtils.isBlank(requestDto.getRequestUnitCode())) {
            log.error("付款公司编号不能为空");
            logService.error(requestDto.getSrcDocNo(), "付款公司编号不能为空");
            return R.error("付款公司编号不能为空");
        }
        String selectPayUnit = "select ID,CODE,NAME_CHS from BFADMINORGANIZATION where ORGTYPE='9edf25d1-b991-4dde-90b9-30145422d24d' and CODE=?1";
        log.info(selectPayUnit + ", ?1=" + requestDto.getRequestUnitCode());
        List<Map<String, Object>> rowsOfPayUnit = DBUtil.querySql(selectPayUnit, requestDto.getRequestUnitCode());
        log.info(JSON.toJSONString(rowsOfPayUnit));
        if (rowsOfPayUnit == null || rowsOfPayUnit.isEmpty()) {
            log.error("付款公司编号无效：" + requestDto.getRequestUnitCode());
            logService.error(requestDto.getSrcDocNo(), "付款公司编号无效：" + requestDto.getRequestUnitCode());
            return R.error("付款公司编号无效");
        }
        String payUnitId = (String) rowsOfPayUnit.get(0).get("ID");
        String payUnitName = (String) rowsOfPayUnit.get(0).get("NAME_CHS");
        logEntity.setRequestUnitId(payUnitId);
        logEntity.setRequestUnitName(payUnitName);
        if (StringUtils.isBlank(requestDto.getExtPayMethod()) && !"HLY".equals(requestDto.getSrcBizSys())) {
            if (!"0".equals(requestDto.getExtDirectPay())) {
                log.error("非资金付款排程安排时支付方式不能为空");
                logService.error(requestDto.getSrcDocNo(), "非资金付款排程安排时支付方式不能为空");
                return R.error("非资金付款排程安排时支付方式不能为空");
            }
        } else {
            log.info("根据支付方式从值映射设置获取结算方式：" + requestDto.getExtPayMethod());

            String paymentMethod = requestDto.getExtPayMethod();

            // 如果是HLY，要根据JFKJPAYMETHODSETTING配置的PAYMENTMETHOD获取结算方式
            if ("HLY".equals(requestDto.getSrcBizSys())) {
                String selectPaymentMethod = "select PAYMENTMETHOD from JFKJPAYMETHODSETTING where SRCBIZSYS=?1 and PAYUNIT=?2";
                log.info(selectPaymentMethod + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getRequestUnitCode());
                List<Map<String, Object>> rowsOfPaymentMethod = DBUtil.querySql(selectPaymentMethod, requestDto.getSrcBizSys(), requestDto.getRequestUnitCode());
                log.info(JSON.toJSONString(rowsOfPaymentMethod));
                if (rowsOfPaymentMethod != null && !rowsOfPaymentMethod.isEmpty()) {
                    paymentMethod = (String) rowsOfPaymentMethod.get(0).get("PAYMENTMETHOD");
                }
            }

            // （二开）通用数据映射配置：待付池-支付方式映射
            String selectPayMethodMapping = "select t.CODE AS SRCCOL01, -- 来源系统标识\n" +
                    "t.NAME AS SRCCOL02, -- 来源支付方式编号\n" +
                    "t.TXT01 AS SRCCOL03, -- 来源支付方式名称\n" +
                    "d.TARCOL01,d.TARCOL01CODE,d.TARCOL01NAME, -- 司库结算方式\n" +
                    "d.TARCOL02, -- 票据支付方式(1,开票;2,背书)\n" +
                    "d.TARCOL03,d.TARCOL03CODE,d.TARCOL03NAME -- 供应链产品\n" +
                    "from IDD_DATADICTIONARY t left join IDD_VMDATA d on t.ID = d.DATAID and d.VMTYPE = 'ebd2a42f-3f40-2e66-4805-5fabd1078342'\n" +
                    "where CATEGORYID='3a6b4bb2-2548-6bf3-c17b-3ce189e2081c' and t.CODE=?1 and t.NAME=?2";
            log.info(selectPayMethodMapping + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + paymentMethod);
            List<Map<String, Object>> rowsOfPayMethodMapping = DBUtil.querySql(selectPayMethodMapping, requestDto.getSrcBizSys(), paymentMethod);
            log.info(JSON.toJSONString(rowsOfPayMethodMapping));
            if (rowsOfPayMethodMapping == null || rowsOfPayMethodMapping.isEmpty()) {
                log.error("支付方式未映射：" + paymentMethod);
                logService.error(requestDto.getSrcDocNo(), "支付方式未映射：" +paymentMethod);
                return R.error("支付方式未映射");
            } else if (StringUtil.isNullOrEmpty((String)rowsOfPayMethodMapping.get(0).get("TARCOL01"))
                    && StringUtil.isNullOrEmpty((String)rowsOfPayMethodMapping.get(0).get("TARCOL02"))
                    && StringUtil.isNullOrEmpty((String)rowsOfPayMethodMapping.get(0).get("TARCOL03"))
            ) {
                if ("FSSC".equals(requestDto.getSrcBizSys()) && StringUtil.isNullOrEmpty(requestDto.getRefSrcDocId()) &&
                        "9".equals(requestDto.getExtPayMethod())) {
                    if (!StringUtil.isNullOrEmpty((String)rowsOfPayMethodMapping.get(0).get("SRCCOL03"))) {
                        logEntity.setSrcPayMethodName((String)rowsOfPayMethodMapping.get(0).get("SRCCOL03"));
                    }
                    log.info("共享系统组合支付方式时推送待付池拆分处理：" + paymentMethod);
                    logService.info(requestDto.getSrcDocNo(), "共享系统组合支付方式时推送待付池拆分处理：" + paymentMethod);
                    logEntity.setDirectPay("0");
                }else{
                    log.error("支付方式未映射：" + paymentMethod);
                    logService.error(requestDto.getSrcDocNo(), "支付方式未映射：" +paymentMethod);
                    return R.error("支付方式未映射");
                }
            }else {
                String payMethodCode = (String)rowsOfPayMethodMapping.get(0).get("SRCCOL02");
                logEntity.setSrcPayMethodCode(payMethodCode);
                String payMethodName = (String)rowsOfPayMethodMapping.get(0).get("SRCCOL03");
                logEntity.setSrcPayMethodName(payMethodName);
                // 结算方式ID
                String expSettlewayId = (String)rowsOfPayMethodMapping.get(0).get("TARCOL01");
                String expSettlewayCode = (String)rowsOfPayMethodMapping.get(0).get("TARCOL01CODE");
                // 票据支付方式：1、2
                String expBillPayWay = (String)rowsOfPayMethodMapping.get(0).get("TARCOL02");
                // 供应链产品ID
                String supplyChainProductsId = (String)rowsOfPayMethodMapping.get(0).get("TARCOL03");
                String selectSettleway = "select w.ID,w.CODE,w.NAME_CHS,t.code as TYPECODE,t.IFACCEPTANCEBILL,STATE_ISENABLED\n" +
                        "from BFSETTLEMENTWAY w inner join BFSETTLEMENTWAYTYPE t on w.SETTLEMENTWAYTYPE=t.ID\n" +
                        "where w.ID=?1";
                log.info(selectSettleway + ", ?1=" + expSettlewayId);
                List<Map<String, Object>> rowsOfSettleway = DBUtil.querySql(selectSettleway, expSettlewayId);
                log.info(JSON.toJSONString(rowsOfSettleway));
                if (rowsOfSettleway == null || rowsOfSettleway.isEmpty()) {
                    log.error("支付方式映射的结算方式无效：来源系统=" + requestDto.getExtPayMethod() + "，司库=" + expSettlewayCode);
                    logService.error(requestDto.getSrcDocNo(), "支付方式映射的结算方式无效：来源系统=" + requestDto.getExtPayMethod() + "，司库=" + expSettlewayCode);
                    return R.error("支付方式映射数据无效");
                } else {
                    logEntity.setSettlementWayId(expSettlewayId);
                }
                Character ifAcceptanceBill = (Character)rowsOfSettleway.get(0).get("IFACCEPTANCEBILL");
                if ('1' == ifAcceptanceBill) {
                    if (StringUtil.isNullOrEmpty(expBillPayWay)) {
                        log.error("结算方式为票据时必须定义票据支付方式");
                        logService.error(requestDto.getSrcDocNo(), "结算方式为票据时必须定义票据支付方式");
                        return R.error("结算方式为票据时必须定义票据支付方式");
                    }
                    if (!"1".equals(expBillPayWay) && !"2".equals(expBillPayWay) && !"5".equals(expBillPayWay)) {
                        log.error("支付方式映射的票据结算方式无效：来源系统=" + requestDto.getExtPayMethod() + "，司库=" + expSettlewayCode);
                        logService.error(requestDto.getSrcDocNo(), "支付方式映射的票据结算方式无效：来源系统=" + requestDto.getExtPayMethod() + "，司库=" + expSettlewayCode);
                        return R.error("支付方式映射数据无效");
                    }
                    logEntity.setBillPayWay(Integer.parseInt(expBillPayWay));
                }
                String expSettlementWayType = (String)rowsOfSettleway.get(0).get("TYPECODE");
                if ("016".equals(expSettlementWayType)) {
                    // 数据映射中如果配置了供应链产品，优先使用配置的供应链产品；没有配置时默认取第一条供应链产品
                    String selectSupplyChainProducts = "select ID,BH,MC_CHS,JRJG from TMGYLCP" +
                            (StringUtil.isNullOrEmpty(supplyChainProductsId) ? null : (" where ID='" + supplyChainProductsId + "'"));
                    log.info(selectSupplyChainProducts);
                    List<Map<String, Object>> rowsOfSupplyChainProducts = DBUtil.querySql(selectSupplyChainProducts);
                    log.info(JSON.toJSONString(rowsOfSupplyChainProducts));
                    if (rowsOfSupplyChainProducts == null || rowsOfSupplyChainProducts.isEmpty()) {
                        log.error("结算方式为供应链票据时未找到定义的供应链产品");
                        logService.error(requestDto.getSrcDocNo(), "结算方式为供应链票据时未找到定义的供应链产品");
                        return R.error("结算方式为供应链票据时未找到定义的供应链产品");
                    }
                    logEntity.setSupplyChainProducts(supplyChainProductsId);
                }
            }
        }
        String payAccountId;
        if (!StringUtils.isBlank(requestDto.getPayAccountNo())) {
            log.info("准备检查付款账号：" + requestDto.getPayAccountNo());
            String selectOfPayAcct = "select ID,ACCOUNTNO,ACCOUNTNAME_CHS,OPENACCOUNTUNIT,ACCOUNTSTATUS,ONLINEBANKOPENSTATUS from BFBANKACCOUNTS where ACCOUNTSTATUS=2 and ACCOUNTNO=?1";
            log.info(selectOfPayAcct + ", ?1=" + requestDto.getPayAccountNo());
            List<Map<String, Object>> rowsOfPayAcct = DBUtil.querySql(selectOfPayAcct, requestDto.getPayAccountNo());
            log.info(JSON.toJSONString(rowsOfPayAcct));
            if (rowsOfPayAcct == null || rowsOfPayAcct.isEmpty()) {
                log.error("付款账号无效：" + requestDto.getPayAccountNo());
                logService.error(requestDto.getSrcDocNo(), "付款账号无效：" + requestDto.getPayAccountNo());
                return R.error("付款账号无效");
            }
            payAccountId = (String)rowsOfPayAcct.get(0).get("ID");
            logEntity.setPayAccountId(payAccountId);
            String payAccountName = (String)rowsOfPayAcct.get(0).get("ACCOUNTNAME_CHS");
            logEntity.setPayAccountName(payAccountName);
            String openUnitId = (String)rowsOfPayAcct.get(0).get("OPENACCOUNTUNIT");
            logEntity.setPayAccountOpenUnitId(openUnitId);
            String openOnlineBankStatus = String.valueOf(rowsOfPayAcct.get(0).get("ONLINEBANKOPENSTATUS"));
            logEntity.setIsBankCommPay("3".equals(openOnlineBankStatus) ? true : false);
            log.info("准备检查付款账户使用权限：accountNo=" + requestDto.getPayAccountNo() + ", unitCode=" + requestDto.getRequestUnitCode());
            String selectAccountAuthUnit = "select ID,STARTEDSTATUS from BFBANKACCOUNTAUTHORIZED WHERE STARTEDSTATUS=2 AND PARENTID='" + payAccountId + "' AND AUTHORIZEDUNIT='" + payUnitId + "'";
            log.info(selectAccountAuthUnit);
            List<Map<String, Object>> rowsOfAccountAuthUnit = DBUtil.querySql(selectAccountAuthUnit);
            log.info(JSON.toJSONString(rowsOfAccountAuthUnit));
            if (rowsOfAccountAuthUnit == null || rowsOfAccountAuthUnit.isEmpty()) {
                log.error("付款公司没有付款账户的使用权限：unitCode=" + requestDto.getRequestUnitCode() + ", accountNo=" + requestDto.getPayAccountNo());
                logService.error(requestDto.getSrcDocNo(), "付款公司没有付款账户的使用权限：unitCode=" + requestDto.getRequestUnitCode() + ", accountNo=" + requestDto.getPayAccountNo());
                return R.error("付款公司没有付款账户的使用权限");
            }
        } else {
            // （二开）付款方式设置表
            JfskBankAccountEntity findBankAccount = null;
            if (JfskUnipayConst.SFS.equals(requestDto.getSrcBizSys())) {
                if (!StringUtils.isBlank(requestDto.getExtPayMethod())) {
                    log.info("1.1、SFS系统传入支付方式时：按支付方式、采购组织获取默认账户");
                    RD<JfskBankAccountEntity> getDefaultPayAccount = defaultPayAccountService.getDefaultPayAccount(logService, requestDto.getSrcBizSys(), requestDto.getSrcDocNo(), requestDto.getRequestUnitCode(), requestDto.getExtPurchase(), requestDto.getExtPayMethod());
                    if (!getDefaultPayAccount.getResult()) {
                        log.error("查询默认付款账户失败：" + getDefaultPayAccount.getMessage());
                        logService.error(requestDto.getSrcDocNo(), "查询默认付款账户失败：" + getDefaultPayAccount.getMessage());
                        return R.error(getDefaultPayAccount.getMessage());
                    }
                    findBankAccount = getDefaultPayAccount.getData();
                } else {
                    log.info("1.2、SFS系统未传入支付方式时：付款安排时可空、非付款安排时不允许为空");
                    if ("0".equals(requestDto.getExtDirectPay())) {
                        log.info("1.2.1、SFS系统不传入支付方式：资金排程付款安排时，在付款安排时获取默认账户");
                        logService.info(requestDto.getSrcDocNo(), "SFS系统不传入支付方式：在付款安排时获取默认账户");
                    } else {
                        log.error("1.2.2、SFS系统不传入支付方式：非资金排程付款安排时付款账号不能为空");
                        logService.error(requestDto.getSrcDocNo(), "SFS系统非资金排程付款安排时付款账号不能为空");
                        return R.error("非资金排程付款安排时付款账号不能为空");
                    }
                }
            } else {
                // CES、HLY按照付款单位+科目编号（TXT01）查找
                log.info("2、CES/HLY系统：按科目编号从配置表加载默认付款账号");
                logService.info(requestDto.getSrcDocNo(), "2、CES/HLY系统：按科目编号从配置表加载默认付款账号");
                RD<JfskBankAccountEntity> getDefaultPayAccount = defaultPayAccountService.getDefaultPayAccount(logService, requestDto.getSrcBizSys(), requestDto.getSrcDocNo(), requestDto.getRequestUnitCode(), requestDto.getTxt01());
                if (!getDefaultPayAccount.getResult()) {
                    log.error("查询默认付款账户失败：" + getDefaultPayAccount.getMessage());
                    logService.error(requestDto.getSrcDocNo(), "查询默认付款账户失败：" + getDefaultPayAccount.getMessage());
                    return R.error(getDefaultPayAccount.getMessage());
                }else{
                    findBankAccount = getDefaultPayAccount.getData();
                }
            }
            if (findBankAccount != null) {
                payAccountId = findBankAccount.getId();
                logEntity.setPayAccountId(payAccountId);
                logEntity.setPayAccountNo(findBankAccount.getAccountNo());
                logEntity.setPayAccountName(findBankAccount.getAccountName());
                logEntity.setPayAccountOpenUnitId(findBankAccount.getOpenAccountUnit());
            }
        }
        log.info("准备检查申请人：Code=" + requestDto.getApplicantCode() + ", Name=" + requestDto.getApplicantName());
        String selectApplicant = "select ID,CODE,NAME_CHS from GSPUSER where CODE=?1";
        log.info(selectApplicant + ", ?1=" + requestDto.getApplicantCode());
        List<Map<String, Object>> rowsOfApplicant = DBUtil.querySql(selectApplicant, requestDto.getApplicantCode());
        log.info(JSON.toJSONString(rowsOfApplicant));
        if (rowsOfApplicant == null || rowsOfApplicant.isEmpty()) {
            log.error("申请人编号无效：" + requestDto.getApplicantCode());
            logService.error(requestDto.getSrcDocNo(), "申请人编号无效：" + requestDto.getApplicantCode());
            return R.error("申请人编号无效");
        }
        String applicantId = (String) rowsOfApplicant.get(0).get("ID");
        logEntity.setApplicantId(applicantId);
        if (!StringUtils.isBlank(requestDto.getRequestDeptCode())) {
            log.info("准备检查申请部门：" + requestDto.getRequestDeptCode());
            String selectDept = "select ID,CODE,NAME_CHS from BFADMINORGANIZATION where ORGTYPE='33c21504-3384-42a7-8fc3-c1b5e9e982d0' and CODE=?1";
            log.info(selectDept + ", ?1=" + requestDto.getRequestDeptCode());
            List<Map<String, Object>> rowsOfDept = DBUtil.querySql(selectDept, requestDto.getRequestDeptCode());
            log.info(JSON.toJSONString(rowsOfDept));
            if (rowsOfDept == null || rowsOfDept.isEmpty()) {
                log.error("申请部门无效：" + requestDto.getRequestDeptCode());
                logService.error(requestDto.getSrcDocNo(), "申请部门无效：" + requestDto.getRequestDeptCode());
                return R.error("申请部门无效");
            }
            String requestDeptId = (String) rowsOfDept.get(0).get("ID");
            logEntity.setRequestDeptId(requestDeptId);
        }
        if (StringUtils.isBlank(requestDto.getReceivingUnitCode())) {
            log.error("供应商编号不能为空");
            logService.error(requestDto.getSrcDocNo(), "供应商编号不能为空");
            return R.error("供应商编号不能为空");
        }
        String partnerId = null;
        String partnerName = null;
        if (!StringUtils.isBlank(requestDto.getReceivingUnitCode())) {
            log.info("准备检查供应商：" + requestDto.getReceivingUnitCode());
            String selectOfPartner = "select ID,CODE,NAME_CHS from BFPARTNER where STATE_ISENABLED='1' AND CODE=?1";
            log.info(selectOfPartner + ", ?1=" + requestDto.getReceivingUnitCode());
            List<Map<String, Object>> rowsOfPartner = DBUtil.querySql(selectOfPartner, requestDto.getReceivingUnitCode());
            log.info(JSON.toJSONString(rowsOfPartner));
            if (rowsOfPartner == null || rowsOfPartner.isEmpty()) {
                log.error("供应商编号无效：" + requestDto.getReceivingUnitCode());
                logService.error(requestDto.getSrcDocNo(), "供应商编号无效：" + requestDto.getReceivingUnitCode());
                return R.error("供应商编号无效");
            }
            partnerId = (String) rowsOfPartner.get(0).get("ID");

            if (StringUtils.isBlank(requestDto.getReceivingUnitName())) {
                log.error("供应商名称无效：" + requestDto.getReceivingUnitCode());
                logService.error(requestDto.getSrcDocNo(), "供应商名称无效：" + requestDto.getReceivingUnitCode());
                return R.error("供应商名称无效");
            }
//            if (!StringUtils.equals(partnerName, requestDto.getReceivingUnitName())) {
//                log.error("司库维护供应商名称与供应商传值不一致：" + requestDto.getReceivingUnitCode());
//                logService.error(requestDto.getSrcDocNo(), "司库维护供应商名称与供应商传值不一致：" + requestDto.getReceivingUnitCode());
//                return R.error("司库维护供应商名称与供应商传值不一致");
//            }
            logEntity.setReceivingUnitId(partnerId);
        } else {
            if ("0".equals(requestDto.getExtDirectPay())) {
                log.error("需资金排程付款安排时供应商编号不能为空");
                logService.error(requestDto.getSrcDocNo(), "需资金排程付款安排时供应商编号不能为空");
                return R.error("需资金排程付款安排时供应商编号不能为空");
            }
            if (StringUtil.isNullOrEmpty(requestDto.getReceivingBankAccountNo())) {
                log.error("供应商编号与收款方账号不能同时为空");
                logService.error(requestDto.getSrcDocNo(), "供应商编号与收款方账号不能同时为空");
                return R.error("供应商编号与收款方账号不能同时为空");
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getReceivingBankAccountNo())) {
            log.info("收款账号非空时检查收款方信息");
            if (StringUtil.isNullOrEmpty(requestDto.getReceivingBankNo())) {
                log.error("收款银行联行号不能为空");
                logService.error(requestDto.getSrcDocNo(), "收款银行联行号不能为空");
                return R.error("收款银行联行号不能为空");
            }
            String selectOppBank = "select ID,CODE,NAME_CHS,COUNTRYORREGION,PROVINCE,CITY from BFBANK where STATE_ISENABLED='1' and BANKIDENTIFIER=?1";
            log.info(selectOppBank + ", ?1=" + requestDto.getReceivingBankNo());
            List<Map<String, Object>> rowsOfOppBank = DBUtil.querySql(selectOppBank, requestDto.getReceivingBankNo());
            log.info(JSON.toJSONString(rowsOfOppBank));
            if (rowsOfOppBank != null && !rowsOfOppBank.isEmpty()) {
                String oppBankId = (String) rowsOfOppBank.get(0).get("ID");
                String oppBankName = (String) rowsOfOppBank.get(0).get("NAME_CHS");
                logEntity.setReceivingBankId(oppBankId);
                logEntity.setReceivingBankName(oppBankName);
                String country = (String)rowsOfOppBank.get(0).get("COUNTRYORREGION");
                logEntity.setReciprocalCountry(country);
                String province = (String)rowsOfOppBank.get(0).get("PROVINCE");
                logEntity.setReciprocalProvinceName(province);
                String city = (String)rowsOfOppBank.get(0).get("CITY");
                logEntity.setReciprocalCityName(city);
            } else {
                log.error("收款银行联行号无效：" + requestDto.getReceivingBankNo());
                logService.error(requestDto.getSrcDocNo(), "收款银行联行号无效：" + requestDto.getReceivingBankNo());
                return R.error("收款银行联行号无效");
            }
            if (StringUtil.isNullOrEmpty(requestDto.getReceivingBankAccountName())) {
                log.error("收款账户名不能为空");
                logService.error(requestDto.getSrcDocNo(), "收款账户名不能为空");
                return R.error("收款账户名不能为空");
            }


            if ("FSSC".equals(request.getSrcBizSys()) && request.getSrcDocType().startsWith("资金调拨同名")) {
                // 资金调拨时，银行账户从账户信息获取
                String selectOppAccount = "select a.ID from BFBANKACCOUNTS a inner join BFBANK b on a.BANK=b.ID where a.ACCOUNTSTATUS='2' and a.ACCOUNTNO=?1 and a.ACCOUNTNAME_CHS=?2 and b.BANKIDENTIFIER=?3";
                log.info(selectOppAccount + ", ?1=" + requestDto.getReceivingBankAccountNo() + ", ?2=" + requestDto.getReceivingBankAccountName() + ", ?3=" + requestDto.getReceivingBankNo());
                List<Map<String, Object>> rowsOfOppAccount = DBUtil.querySql(selectOppAccount, requestDto.getReceivingBankAccountNo(), requestDto.getReceivingBankAccountName(), requestDto.getReceivingBankNo());
                log.info(JSON.toJSONString(rowsOfOppAccount));
                if (rowsOfOppAccount != null && !rowsOfOppAccount.isEmpty()) {
                    String oppAcctId = (String) rowsOfOppAccount.get(0).get("ID");
                    logEntity.setReceivingBankAccountId(oppAcctId);
                }
            }else{
                // 其他情况，银行账户从往来单位银行账户获取
                String selectOppAccount = "select a.ID,a.INBANK,a.PARTNERID from BFPARTNERBANKACCOUNTS a inner join BFBANK b on a.INBANK=b.ID where a.STATE_ISENABLED='1' and a.ACCOUNTCODE=?1 and a.ACCOUNTNAME_CHS=?2 and b.BANKIDENTIFIER=?3";
                log.info(selectOppAccount + ", ?1=" + requestDto.getReceivingBankAccountNo() + ", ?2=" + requestDto.getReceivingBankAccountName() + ", ?3=" + requestDto.getReceivingBankNo());
                List<Map<String, Object>> rowsOfOppAccount = DBUtil.querySql(selectOppAccount, requestDto.getReceivingBankAccountNo(), requestDto.getReceivingBankAccountName(), requestDto.getReceivingBankNo());
                log.info(JSON.toJSONString(rowsOfOppAccount));
                if (rowsOfOppAccount != null && !rowsOfOppAccount.isEmpty()) {
                    String oppAcctId = (String) rowsOfOppAccount.get(0).get("ID");
                    logEntity.setReceivingBankAccountId(oppAcctId);
                }
            }
        } else {
            if ("FSSC".equals(request.getSrcBizSys()) && request.getSrcDocType().startsWith("资金调拨同名")) {
                // 资金调拨时，银行账户从账户信息获取   
                log.error("调拨场景无法从对方单位直接获取账户信息");
                logService.error(requestDto.getSrcDocNo(), "调拨场景无法从对方单位直接获取账户信息");
                return R.error("调拨场景无法从对方单位直接获取账户信息");
                
            }else{
                // 其他情况，银行账户从往来单位银行账户获取
                String selectDefaultOppAccount = "select a.ID,a.ACCOUNTCODE,a.ACCOUNTNAME_CHS,b.ID as BANKID,b.BANKIDENTIFIER,b.NAME_CHS as BANKNAME from BFPARTNERBANKACCOUNTS a inner join BFBANK b on a.INBANK=b.ID where a.STATE_ISENABLED='1' and a.PARTNERID=?1";
                log.info(selectDefaultOppAccount + ", ?1=" + partnerId);
                List<Map<String, Object>> rowsOfDefaultOppAccount = DBUtil.querySql(selectDefaultOppAccount, partnerId);
                log.info(JSON.toJSONString(rowsOfDefaultOppAccount));
                if (rowsOfDefaultOppAccount == null || rowsOfDefaultOppAccount.isEmpty()) {
                    log.error("供应商未维护银行账户时收款账号不能为空：" + requestDto.getReceivingUnitCode());
                    logService.error(requestDto.getSrcDocNo(), "供应商未维护银行账户时收款账号不能为空：" + requestDto.getReceivingUnitCode());
                    return R.error("供应商未维护银行账户时收款账号不能为空");
                }
                String oppBankId = (String) rowsOfDefaultOppAccount.get(0).get("BANKID");
                String oppAccountId = (String) rowsOfDefaultOppAccount.get(0).get("ID");
                logEntity.setReceivingBankId(oppBankId);
                logEntity.setReceivingBankAccountId(oppAccountId);
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getReceivingBankNo()) && !StringUtil.isNullOrEmpty(requestDto.getReceivingBankAccountNo())) {
            log.info("准备检查收款账号是否内部单位账号");
            String oppAccountNo = requestDto.getReceivingBankAccountNo();
            String oppBankNo = requestDto.getReceivingBankNo();
            String oppAccountName = requestDto.getReceivingBankAccountName();
            String selectIfInnerAccount = "select a.ID,a.OPENACCOUNTUNIT,b.ID as BANKID from BFBANKACCOUNTS a inner join BFBANK b on a.BANK=b.ID where ACCOUNTNO=?1 and ACCOUNTNAME_CHS=?2 and b.BANKIDENTIFIER=?3";
            log.info(selectIfInnerAccount + ", ?1=" + oppAccountNo + ", ?2=" + oppAccountName + ", ?3=" + oppBankNo);
            List<Map<String, Object>> rowsOfIfInnerAccount = DBUtil.querySql(selectIfInnerAccount, oppAccountNo, oppAccountName, oppBankNo);
            log.info(JSON.toJSONString(rowsOfIfInnerAccount));
            if (rowsOfIfInnerAccount != null && !rowsOfIfInnerAccount.isEmpty()) {
                String openUnitId = (String) rowsOfIfInnerAccount.get(0).get("OPENACCOUNTUNIT");
                logEntity.setReceivingBankAccountOpenUnitId(openUnitId);
            }
        }
        log.info("准备检查币种：" + requestDto.getCurrencyCode());
        if(StringUtil.isNullOrEmpty(requestDto.getCurrencyCode())){
            return R.error("币种无效");
        }
        String selectCurrency = "select ID,CODE,NAME_CHS from BFCURRENCY where STATE_ISENABLED='1' AND CODE=?1";
        log.info(selectCurrency + ", ?1=" + requestDto.getCurrencyCode());
        List<Map<String, Object>> rowsOfCurrency = DBUtil.querySql(selectCurrency, requestDto.getCurrencyCode());
        log.info(JSON.toJSONString(rowsOfCurrency));
        if (rowsOfCurrency == null || rowsOfCurrency.isEmpty()) {
            log.error("币种无效：" + requestDto.getCurrencyCode());
            logService.error(requestDto.getSrcDocNo(), "币种无效：" + requestDto.getCurrencyCode());
            return R.error("币种无效");
        }
        String currencyId = (String) rowsOfCurrency.get(0).get("ID");
        logEntity.setCurrencyId(currencyId);
        if (!StringUtil.isNullOrEmpty(requestDto.getTransCurrencyCode())) {
            log.info("准备检查交易币种：" + requestDto.getTransCurrencyCode());
            String selectTransCurrency = "select ID,CODE,NAME_CHS from BFCURRENCY where STATE_ISENABLED='1' AND CODE=?1";
            log.info(selectTransCurrency + ", ?1=" + requestDto.getTransCurrencyCode());
            List<Map<String, Object>> rowsOfTransCurrency = DBUtil.querySql(selectTransCurrency, requestDto.getTransCurrencyCode());
            log.info(JSON.toJSONString(rowsOfTransCurrency));
            if (rowsOfTransCurrency == null || rowsOfTransCurrency.isEmpty()) {
                log.error("交易币种无效：" + requestDto.getTransCurrencyCode());
                logService.error(requestDto.getSrcDocNo(), "交易币种无效：" + requestDto.getTransCurrencyCode());
                return R.error("交易币种无效");
            }
            String transCurrencyId = (String) rowsOfTransCurrency.get(0).get("ID");
            logEntity.setTransCurrencyId(transCurrencyId);
            if (requestDto.getTransAmount() == null) {
                log.error("交易金额不能为空");
                logService.error(requestDto.getSrcDocNo(), "交易金额不能为空");
                return R.error("交易金额不能为空");
            } else {
                if (BigDecimal.ZERO.compareTo(requestDto.getTransAmount()) >= 0) {
                    log.error("交易金额无效：" + requestDto.getTransAmount());
                    logService.error(requestDto.getSrcDocNo(), "交易金额无效：" + requestDto.getTransAmount());
                    return R.error("交易金额无效");
                }
            }
            if (requestDto.getTransExchangeRate() == null) {
                log.error("交易汇率不能为空");
                logService.error(requestDto.getSrcDocNo(), "交易汇率不能为空");
                return R.error("交易汇率不能为空");
            } else {
                if (BigDecimal.ZERO.compareTo(requestDto.getTransExchangeRate()) >= 0) {
                    log.error("交易汇率无效：" + requestDto.getTransExchangeRate());
                    logService.error(requestDto.getSrcDocNo(), "交易汇率无效：" + requestDto.getTransExchangeRate());
                    return R.error("交易汇率无效");
                }
            }
        }
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (!StringUtil.isNullOrEmpty(requestDto.getOriginalApplyDate())) {
            log.info("准备检查申请日期：" + requestDto.getOriginalApplyDate());
            try {
                sdf.parse(requestDto.getOriginalApplyDate());
            } catch (Throwable ex) {
                log.error("解析申请日期出错：", ex);
                logService.error(requestDto.getSrcDocNo(), "解析申请日期出错：" + ExceptionUtils.getStackTrace(ex));
                return R.error("申请日期无效");
            }
        }
        if (!StringUtil.isNullOrEmpty(requestDto.getOriginalExpPayDate())) {
            log.info("准备检查期望付款日期：" + requestDto.getOriginalExpPayDate());
            try {
                sdf.parse(requestDto.getOriginalExpPayDate());
            } catch (Throwable ex) {
                log.error("解析期望付款日期出错：", ex);
                logService.error(requestDto.getSrcDocNo(), "解析期望付款日期出错：" + ExceptionUtils.getStackTrace(ex));
                return R.error("期望付款日期无效");
            }
        }
        if (requestDto.getRequestAmount() == null) {
            log.error("申请金额不能为空");
            logService.error(requestDto.getSrcDocNo(), "申请金额不能为空");
            return R.error("申请金额不能为空");
        } else {
            if (BigDecimal.ZERO.compareTo(requestDto.getRequestAmount()) >= 0) {
                log.error("申请金额无效：" + requestDto.getRequestAmount());
                logService.error(requestDto.getSrcDocNo(), "申请金额无效：" + requestDto.getRequestAmount());
                return R.error("申请金额无效");
            }
        }
        if (StringUtil.isNullOrEmpty(requestDto.getSummary())) {
            log.error("银行摘要不能为空");
            logService.error(requestDto.getSrcDocNo(), "摘要不能为空");
            return R.error("银行摘要不能为空");
        } else {
            try {
                int length = requestDto.getSummary().getBytes(StandardCharsets.UTF_8).length;
                log.info("摘要长度" + length);
                if (length > 90) {
                    log.error("银行摘要长度不能超过90个字符：" + requestDto.getSummary());
                    logService.error(requestDto.getSrcDocNo(), "银行摘要长度不能超过90个字符：" + requestDto.getSummary());
                    return R.error("银行摘要长度不能超过90个字符");
                }
            } catch (Throwable ex) {
                log.error("银行摘要长度检查时出错：", ex);
                logService.error(requestDto.getSrcDocNo(), "银行摘要长度检查时出错：" + ExceptionUtils.getStackTrace(ex));
                return R.error("银行摘要不是有效的UTF8文本");
            }
        }
        /* todo: 利润中心
        if (StringUtil.isNullOrEmpty(requestDto.getExtProfitCenter())) {
            RD<String> getProfitCenter = profitCenterService.getProfitCenter(requestDto.getExtProjectWbsCode(), requestDto.getExtCostCenter(), requestDto.getExtInnerOrder());
            if (!getProfitCenter.getResult()) {
                log.error("未能获取到利润中心：" + getProfitCenter.getMessage());
                logService.error(requestDto.getSrcDocNo(), "未能获取到利润中心：" + getProfitCenter.getMessage());
                return R.error(getProfitCenter.getMessage());
            }
            logEntity.setExtProfitCenter(getProfitCenter.getData());
        }*/
        if (!StringUtil.isNullOrEmpty(requestDto.getRefSrcDocId())) {
            log.info("准备检查共享拆单关联原付款申请：" + requestDto.getRefSrcDocId());
            String selectRefSrcDoc = "select ID,SRCBIZSYS,SRCDOCTYPE,SRCDOCID,DOCSTATUS from JTGKPAYMENTINFO where DOCSTATUS in (1,-3)" +
                    " and SRCBIZSYS='" + requestDto.getSrcBizSys() + "'" +
                    (StringUtil.isNullOrEmpty(requestDto.getSrcDocType()) ? "" : (" and SRCDOCTYPE='" + requestDto.getSrcDocType() + "'")) +
                    " and SRCDOCID='" + requestDto.getRefSrcDocId() + "'";
            log.info(selectRefSrcDoc);
            List<Map<String, Object>> rowsOfRefSrcDoc = DBUtil.querySql(selectRefSrcDoc);
            log.info(JSON.toJSONString(rowsOfRefSrcDoc));
            if (rowsOfRefSrcDoc == null || rowsOfRefSrcDoc.isEmpty()) {
                log.error("共享拆分支付时关联父单据唯一ID无效：" + requestDto.getRefSrcDocId());
                logService.error(requestDto.getSrcDocNo(), "共享拆分支付时关联父单据唯一ID无效：" + requestDto.getRefSrcDocId());
                return R.error("共享拆分支付时关联父单据唯一ID无效");
            }
        }
        if (StringUtil.isNullOrEmpty(requestDto.getFundNatureCode())) {
            log.info("根据来源系统标识、单据类型获取默认款项性质");
            String selectDefaultFundsProp = "select TXT01 from IDD_DATADICTIONARY where CATEGORYID='ec10e45e-7468-96cd-3127-e1b7b2616ad1' and CODE=?1 and NAME=?2";
            log.info(selectDefaultFundsProp + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getSrcDocType());
            List<Map<String, Object>> rowsOfDefaultFundsProp = DBUtil.querySql(selectDefaultFundsProp, requestDto.getSrcBizSys(), requestDto.getSrcDocType());
            log.info(JSON.toJSONString(rowsOfDefaultFundsProp));
            if (rowsOfDefaultFundsProp == null || rowsOfDefaultFundsProp.isEmpty()) {
                log.info("默认款项性质未定义：" + requestDto.getSrcBizSys() + ", srcDocType=" + requestDto.getSrcDocType());
                logService.info(requestDto.getSrcDocNo(), "默认款项性质未定义：" + requestDto.getSrcBizSys() + ", srcDocType=" + requestDto.getSrcDocType());
//                return R.error("默认款项性质未定义");
            } else {
                String fundsId = (String)rowsOfDefaultFundsProp.get(0).get("ID");
                logEntity.setFundnatureId(fundsId);
            }
        } else {
            log.info("根据原因码从值映射设置获取款项性质：" + requestDto.getFundNatureCode());
            String selectFundsProp = "select s.ID,s.CODE,s.NAME_CHS from GSPVMVALUELIST m inner join BFFUNDSPROP s on m.TARCOL=s.ID" +
                    " where VMID='" + JfskUnipayConst.ValueMapping_FundNature + "' and SRCCOL01=?1 AND SRCCOL02=?2";
            log.info(selectFundsProp + ", ?1=" + requestDto.getSrcBizSys() + ", ?2=" + requestDto.getFundNatureCode());
            List<Map<String, Object>> rowsOfFundsProp = DBUtil.querySql(selectFundsProp, requestDto.getSrcBizSys(), requestDto.getFundNatureCode());
            log.info(JSON.toJSONString(rowsOfFundsProp));
            if (rowsOfFundsProp == null || rowsOfFundsProp.isEmpty()) {
                log.info("款项性质未映射或未定义：" + requestDto.getFundNatureCode());
                logService.info(requestDto.getSrcDocNo(), "款项性质未映射或未定义：" + requestDto.getFundNatureCode());
//                return R.error("款项性质未映射或未定义");
            } else {
                String fundsId = (String)rowsOfFundsProp.get(0).get("ID");
                logEntity.setFundnatureId(fundsId);
            }
        }
        return R.ok();
    }

    /**
     * 根据请求入参生成接口日志记录
     * @param requestDto 请求入参
     * @return 接口日志记录
     */
    public static JfskPaymentInfoEntity createLogEntity(PaymentInfoDto requestDto) {
        JfskPaymentInfoEntity logEntity = new JfskPaymentInfoEntity();
        logEntity.setId(UUID.randomUUID().toString());
        logEntity.setSrcBizSys(requestDto.getSrcBizSys());
        logEntity.setSrcDocType(requestDto.getSrcDocType());
        logEntity.setSrcDocId(requestDto.getSrcDocId());
        logEntity.setSrcDocNo(requestDto.getSrcDocNo());
        logEntity.setRequestUnitCode(requestDto.getRequestUnitCode());
        logEntity.setPayAccountNo(requestDto.getPayAccountNo());
        logEntity.setApplicantCode(requestDto.getApplicantCode());
        logEntity.setApplicantName(requestDto.getApplicantName());
        logEntity.setRequestDeptCode(requestDto.getRequestDeptCode());
        // 新增内容 - 如果申请日期为空使用当前系统日期
        if (StringUtils.isEmpty(requestDto.getOriginalApplyDate())) {
            // 把当前日期转换为yyyy-MM-dd格式
            logEntity.setApplyDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        } else {
            logEntity.setApplyDate(requestDto.getOriginalApplyDate());
        }
        logEntity.setReceivingUnitCode(requestDto.getReceivingUnitCode());
        logEntity.setReceivingUnitName(requestDto.getReceivingUnitName());
        logEntity.setReceivingBankNo(requestDto.getReceivingBankNo());
        logEntity.setReceivingBankName(requestDto.getReceivingBankName());
        logEntity.setReceivingBankAccountNo(requestDto.getReceivingBankAccountNo());
        logEntity.setReceivingBankAccountName(requestDto.getReceivingBankAccountName());
        logEntity.setCurrencyCode(requestDto.getCurrencyCode());
        logEntity.setRequestAmount(requestDto.getRequestAmount());
        logEntity.setTransAmount(requestDto.getTransAmount());
        logEntity.setTransCurrencyCode(requestDto.getTransCurrencyCode());
        logEntity.setTransExchangeRate(requestDto.getTransExchangeRate());
        // 新增内容 - 如果期望付款日期为空使用当前系统日期
        if (StringUtils.isEmpty(requestDto.getOriginalExpPayDate())) {
            logEntity.setExpectPayDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        } else {
            logEntity.setExpectPayDate(requestDto.getOriginalExpPayDate());
        }
        logEntity.setDirectPay(requestDto.getExtDirectPay());
        logEntity.setSrcPayMethodCode(requestDto.getExtPayMethod());
        logEntity.setSrcPayMethodName(requestDto.getExtPayMethod());
        logEntity.setSummary(requestDto.getSummary());
        logEntity.setFundnatureCode(requestDto.getFundNatureCode());
        logEntity.setDescription(requestDto.getDescription());
        logEntity.setRefSrcDocId(requestDto.getRefSrcDocId());
        logEntity.setExtPurchaseCode(requestDto.getExtPurchase());
        logEntity.setExtProjectCode(requestDto.getExtProjectWbsCode());
        logEntity.setExtCostCenter(requestDto.getExtCostCenter());
        logEntity.setExtInnerOrder(requestDto.getExtInnerOrder());
        logEntity.setExtProfitCenter(requestDto.getExtProfitCenter());
        logEntity.setLinkUrl(requestDto.getLinkUrl());
        logEntity.setDocStatus(0);
        logEntity.setCreatedOn(new Date());
        logEntity.setLastchangedOn(new Date());
        logEntity.setTxt01(requestDto.getTxt01());
        logEntity.setTxt02(requestDto.getTxt02());
        logEntity.setTxt03(requestDto.getTxt03());
        logEntity.setTxt04(requestDto.getTxt04());
        logEntity.setTxt05(requestDto.getTxt05());
        logEntity.setTxt06(requestDto.getTxt06());
        logEntity.setTxt07(requestDto.getTxt07());
        logEntity.setTxt08(requestDto.getTxt08());
        logEntity.setTxt09(requestDto.getTxt09());
        logEntity.setTxt10(requestDto.getTxt10());
        logEntity.setTxt11(requestDto.getTxt11());
        logEntity.setTxt12(requestDto.getTxt12());
        logEntity.setTxt13(requestDto.getTxt13());
        logEntity.setTxt14(requestDto.getTxt14());
        logEntity.setTxt15(requestDto.getTxt15());
        logEntity.setTxt16(requestDto.getTxt16());
        logEntity.setTxt17(requestDto.getTxt17());
        logEntity.setTxt18(requestDto.getTxt18());
        logEntity.setTxt19(requestDto.getTxt19());
        logEntity.setTxt20(requestDto.getTxt20());
        logEntity.setAmt01(requestDto.getAmt01());
        logEntity.setAmt02(requestDto.getAmt02());
        logEntity.setAmt03(requestDto.getAmt03());
        logEntity.setAmt04(requestDto.getAmt04());
        logEntity.setAmt05(requestDto.getAmt05());
        logEntity.setAmt06(requestDto.getAmt06());
        logEntity.setAmt07(requestDto.getAmt07());
        logEntity.setAmt08(requestDto.getAmt08());
        logEntity.setAmt09(requestDto.getAmt09());
        logEntity.setAmt10(requestDto.getAmt10());
        logEntity.setDate01(requestDto.getDate01());
        logEntity.setDate02(requestDto.getDate02());
        logEntity.setDate03(requestDto.getDate03());
        logEntity.setDate04(requestDto.getDate04());
        logEntity.setDate05(requestDto.getDate05());
        logEntity.setDate06(requestDto.getDate06());
        logEntity.setDate07(requestDto.getDate07());
        logEntity.setDate08(requestDto.getDate08());
        logEntity.setDate09(requestDto.getDate09());
        logEntity.setDate10(requestDto.getDate10());
        return logEntity;
    }

    /**
     * 生成付款单后增加待付池执行记录
     * @param logEntity 待付池记录
     * @param requestDocId 业务支付申请ID
     * @param requestDocNo 业务支付申请单号
     * @return 执行记录
     */
    private static JfskPaymentDetailEntity createDetailEntity(JfskPaymentInfoEntity logEntity, String payDocType, String requestDocId, String requestDocNo) {
        JfskPaymentDetailEntity detailEntity = new JfskPaymentDetailEntity();
        detailEntity.setId(UUID.randomUUID().toString());
        detailEntity.setParentId(logEntity.getId());
        // 原始支付方式
        detailEntity.setSrcPayMethodCode(logEntity.getSrcPayMethodCode());
        detailEntity.setSrcPayMethodName(logEntity.getSrcPayMethodName());
        // 结算方式ID
        detailEntity.setSettlementwayId(logEntity.getSettlementWayId());
        detailEntity.setPaydocType(payDocType);
        if ("0".equals(payDocType)) {
            // 业务支付申请
            detailEntity.setRequestDocId(requestDocId);
            detailEntity.setRequestDocNo(requestDocNo);
        } else {
            // 单位调拨单
            detailEntity.setPaydocId(requestDocId);
            detailEntity.setPaydocNo(requestDocNo);
        }
        detailEntity.setPayAccountId(logEntity.getPayAccountId());
        detailEntity.setAmount(logEntity.getRequestAmount());
        detailEntity.setDocStatus(0);
        detailEntity.setCreatedOn(new Date());
        detailEntity.setLastchangedOn(new Date());
        return detailEntity;
    }
}
