package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.SchedulerLoggerUtil;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.lockservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.text.DecimalFormat;
import java.time.Duration;
import java.util.*;

/**
 * 应收票据贴现到账票据推送sap完成凭证推送
 */
@GspComponent("jtgk-Receivable-Bill-Discount-Sap-Push-Scheduler")
@Controller
@Slf4j
public class JfskReceivableBillSapPushScheduler {

    @Autowired
    private ILockService lockService;
    
    @Autowired
    private LogService logService;
    

    private static final String LOG_CODE = "BILL_DISCOUNT_PUSH_SAP";
    private static final String LOG_CODE_COLLECTION = "BILL_COLLECTION_PUSH_SAP";
    private static final String LOG_CODE_PAYABLE = "BILL_PAYABLE_PUSH_SAP";
    
    // 推送状态定义
    private static final String PUSH_STATUS_PUSHED = "0";     // 已推送
    private static final String PUSH_STATUS_SUCCESS = "2";    // 推送成功
    private static final String PUSH_STATUS_FAILED = "3";     // 推送失败 
    // private static final String PUSH_STATUS_OTHER = "4";      // 其他状态
    
    // 票据认领类型
    private static final String RECEIPT_TYPE_DISCOUNT = "RLLX0003"; // 贴现
    private static final String RECEIPT_TYPE_COLLECTION = "RLLX0001"; // 托收
    private static final String RECEIPT_TYPE_PAYABLE = "RLLX0002"; // 应付到期兑付
    
    // 场景定义
    private static final String SCENE_CODE_DISCOUNT = "803"; // 贴现场景编号
    private static final String SCENE_DESC_DISCOUNT = "803-应收票据贴现到账"; // 贴现场景描述
    private static final String SCENE_CODE_COLLECTION = "804"; // 托收场景编号
    private static final String SCENE_DESC_COLLECTION = "804-应收票据托收到账"; // 托收场景描述
    private static final String SCENE_CODE_PAYABLE = "807"; // 应付票据场景编号
    private static final String SCENE_DESC_PAYABLE = "807-应付票据到期兑付"; // 应付票据场景描述

    /**
     * 应收票据贴现到账票据推送sap完成凭证推送
     */
    public void pushDiscountBillToSap() {
        String jobId = "27c6fc92-ae12-4ef9-b33e-3c1a16bde8ac";
        String jobName = "应收票据贴现到账票据推送SAP";
        String lockId;
        
        try {
            // 获取分布式锁
            String moduleId = "JfskReceivableBillDiscountSap";
            String funcId = "pushDiscountBillToSap";
            String categoryId = "String";
            String dataID = "jtgk-Receivable-Bill-Discount-Sap";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, 
                    new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, 
                            LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                log.error(jobName + "加锁失败");
                return;
            }
            
            lockId = lockResult.getLockId();
            log.info(jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发生异常：" + ex.toString());
            log.error(jobName + "加锁过程发生异常：", ex);
            return;
        }

        logService.init(LOG_CODE);
        try {
            // 处理贴现票据
            processDiscountBillAndPushToSap(logService);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, ex.toString());
            log.error("应收票据贴现到账票据推送SAP过程发生异常：", ex);
            logService.error(LOG_CODE, "应收票据贴现到账票据推送SAP过程发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        
        logService.flush();
        
        try {
            lockService.removeLock(lockId);
            log.info(jobName + "已解锁");
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
            log.error(jobName + "解锁过程发生异常：", ex);
        }
    }

    /**
     * 处理应收票据贴现到账并推送SAP
     * @param logService 日志服务
     */
    private void processDiscountBillAndPushToSap(LogService logService) {
        log.info("开始处理应收票据贴现到账并推送SAP");
        logService.info(LOG_CODE, "开始处理应收票据贴现到账并推送SAP");

        logService.info(LOG_CODE, "开始处理应收票据贴现到SAP相关参数");
        String selectSql = "select CODE, TXT01, TXT02, BIGTXT01 from IDD_DATADICTIONARY where CATEGORYID='b9badc01-6db5-5b10-5813-5311071bbccf' and TXT03='pjywpz'";
        logService.info(LOG_CODE, "执行SQL：{}", selectSql);
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectSql);
        logService.info(LOG_CODE, "查询结果：{}", JSON.toJSONString(rowsOfBizSys));
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            logService.error(LOG_CODE, "未找到业务系统配置");
            logService.flush();
            return;
        }

        // 环境配置
        String MANDT = rowsOfBizSys.get(0).get("CODE").toString();
        String sapBillPushUsername = rowsOfBizSys.get(0).get("TXT01").toString();
        String sapBillPushPassword = rowsOfBizSys.get(0).get("TXT02").toString();
        String sapBillPushUrl = rowsOfBizSys.get(0).get("BIGTXT01").toString();
        
        
        // 查询符合条件的业务收款单
        String querySql = "SELECT distinct br.ID, br.DOCNO, br.RECEIVINGACCOUNT as RECEIVINGACCOUNT, br.RECEIVINGACCOUNTNO as RECEIVINGACCOUNTNO, bi.NAME_CHS AS BANKNAME, br.CURRENCY as CURRENCY, " +
                "br.RECEIVINGUNIT, org.NAME_CHS AS RECEIVINGUNITNAME, org.CODE AS RECEIVINGUNITCODE, payOrg.NAME_CHS AS PAYUNITNAME, payOrg.CODE AS PAYUNITCODE, " +
                "br.TIMESTAMPS_CREATEDON, user_info.NAME_CHS AS USERNAME, " +
                "br.SUMMARY AS SUMMARY, user_info.code as USERCODE, " + // 注意末尾逗号
                "jspl.ID AS existingLogId, jspl.PUSHSTATUS AS existingPushStatus " + // 新增字段
                "FROM BPBusinessReceiving br " +
                "INNER JOIN ZJRLJL rl ON rl.BZDNM = br.ID " +
                "INNER JOIN ZJRLLX lx ON rl.RLLX = lx.ID AND lx.lxbh = '" + RECEIPT_TYPE_DISCOUNT + "' " +
                "LEFT JOIN JTGKSAPPUSHBILLLOG jspl ON br.ID = jspl.pjid " + // 新增JOIN
                "LEFT JOIN BFADMINORGANIZATION org ON br.RECEIVINGUNIT = org.ID " +
                "LEFT JOIN BFPARTNER payOrg ON br.PAYUNIT = payOrg.ID " +
                "LEFT JOIN GSPUser user_info ON br.PRODUCERID = user_info.ID " +
                "LEFT JOIN BFBANKACCOUNTS ba ON br.RECEIVINGACCOUNT = ba.ID " +
                "LEFT JOIN BFBANK bi ON ba.BANK = bi.ID " +
                "WHERE br.docstatus = 3 " +
                "AND (jspl.ID IS NULL OR jspl.PUSHSTATUS NOT IN ('" + PUSH_STATUS_PUSHED + "', '" + PUSH_STATUS_SUCCESS + "')) " + // 新增条件
                "ORDER BY br.TIMESTAMPS_CREATEDON DESC";

        log.info("查询SQL: " + querySql);
        logService.info(LOG_CODE, "查询SQL: " + querySql);
        
        List<Map<String, Object>> receivingBills = DBUtil.querySql(querySql);
        log.info("查询结果: " + JSON.toJSONString(receivingBills));
        logService.info(LOG_CODE, "查询到" + (receivingBills != null ? receivingBills.size() : 0) + "条业务收款单");

        if (receivingBills == null || receivingBills.isEmpty()) {
            log.info("没有查询到符合条件的业务收款单");
            logService.info(LOG_CODE, "没有查询到符合条件的业务收款单");
            return;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        
        // 循环处理每一个业务收款单
        for (Map<String, Object> receivingBill : receivingBills) {
            String billId = (String) receivingBill.get("ID");
            String billNo = (String) receivingBill.get("DOCNO");

            // 为每个billNo单独初始化日志
            logService.init(billNo);

            // 获取业务发生日BUDAT并格式化为yyyyMMdd
            String businessDateSql = "SELECT TRANSACTIONDATE FROM BPBIZBANKTRANSDETAILS WHERE PARENTID = '" + billId + "'";
            List<Map<String, Object>> businessDateList = DBUtil.querySql(businessDateSql);
            String budatValue = ""; // 用于SAP XML和数据库日志。确保 dateFormat 在此作用域内已定义为 new SimpleDateFormat("yyyyMMdd")

            if (businessDateList != null && !businessDateList.isEmpty()) {
                Object transactionDateObj = businessDateList.get(0).get("TRANSACTIONDATE");
                if (transactionDateObj != null) {
                    if (transactionDateObj instanceof java.util.Date) { // 包括 java.sql.Date 和 java.sql.Timestamp
                        try {
                            // dateFormat 实例应为 new SimpleDateFormat("yyyyMMdd")
                            budatValue = dateFormat.format((java.util.Date) transactionDateObj);
                        } catch (Exception e) {
                            log.error("Error formatting TRANSACTIONDATE (Date object) for billId " + billId + " in " + LOG_CODE + ": " + e.getMessage(), e);
                            logService.error(LOG_CODE, "Error formatting TRANSACTIONDATE (Date object) for billId " + billId + ": " + e.getMessage());
                            // budatValue 保持为 ""
                        }
                    } else {
                        // 如果不是 Date 对象，尝试将其作为字符串解析
                        String transactionDateStr = transactionDateObj.toString().trim();
                        if (!transactionDateStr.isEmpty()) {
                            try {
                                Date parsedDate = null;
                                // 尝试常见的日期时间格式，如 "yyyy-MM-dd HH:mm:ss.S", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"
                                if (transactionDateStr.matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}(\\.\\d+)?$")) {
                                    parsedDate = new SimpleDateFormat(transactionDateStr.contains(".") ? "yyyy-MM-dd HH:mm:ss.S" : "yyyy-MM-dd HH:mm:ss").parse(transactionDateStr);
                                } else if (transactionDateStr.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                                    parsedDate = new SimpleDateFormat("yyyy-MM-dd").parse(transactionDateStr);
                                }

                                if (parsedDate != null) {
                                    budatValue = dateFormat.format(parsedDate);
                                } else {
                                    log.warn("TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE + " did not match known date/timestamp patterns for parsing.");
                                    logService.warn(LOG_CODE, "TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE + " did not match known date/timestamp patterns for parsing.");
                                }
                            } catch (java.text.ParseException pe) {
                                log.warn("Could not parse TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE + ". Error: " + pe.getMessage());
                                logService.warn(LOG_CODE, "Could not parse TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE + ". Error: " + pe.getMessage());
                                // budatValue 保持为 ""
                            }
                        }
                    }
                }
            }
            // 原 businessDate 变量现在由 budatValue 替代。
            
            log.info("处理业务收款单: " + billNo);
            logService.info(billNo, "处理业务收款单: " + billNo);
            
            // 新的逻辑，替代旧的 checkPushedSql 及相关处理 (原 Lines 171-195)
            boolean needToUpdate = false;
            String existingLogId = getStringValue(receivingBill.get("EXISTINGLOGID"));
            String existingPushStatus = getStringValue(receivingBill.get("EXISTINGPUSHSTATUS"));
            
            if (existingLogId != null && !existingLogId.isEmpty()) {
                // 如果 existingLogId 存在，说明之前推送过但失败了或状态为其他，需要更新
                log.info("业务收款单" + billNo + "之前推送状态为 " + existingPushStatus + " (LogID: " + existingLogId + ")，将重新推送并更新记录。");
                logService.info(billNo, "业务收款单" + billNo + "之前推送状态为 " + existingPushStatus + " (LogID: " + existingLogId + ")，将重新推送并更新记录。");
                needToUpdate = true;
            } else {
                // existingLogId 为空，说明是新票据，不需要更新，后续直接插入新日志
                log.info("业务收款单" + billNo + "为新票据或之前未成功记录，将进行推送尝试。");
                logService.info(billNo, "业务收款单" + billNo + "为新票据或之前未成功记录，将进行推送尝试。");
            }
            // 旧的 checkPushedSql 块已被此新逻辑和主查询的优化所替代。
            
            // 查询收款单下的票据明细
            String queryBillItems = "SELECT b.ID as ID, b.BILLID AS BILLID, b.BILLNO, b.BILLTYPE, b.BILLAMT, b.DISCOUNTINTEREST, b.BILLDUEDATE, b.DISCOUNTRATE, c.CODE AS CURRENCY, tmb.TXT11 as GSZT, tmb.TXT03 as LRZX, tmb.TXT04 as LRZXNAME, tmr.TXT05 as CBZXBH, tmr.TXT06 as CBZXMC, " +
                    "b.SUBBILLSTARTSN, b.SUBBILLENDSN " +
                    "FROM BPBIZRCVBILLITEM b " +
                    "LEFT JOIN BFCURRENCY c ON b.CURRENCY = c.ID " +
                    "LEFT JOIN TMBILLRECEIVABLEINVENTORY tmb ON b.BILLID = tmb.ID " +
                    "LEFT JOIN TMBILLDISCOUNTDETAIL tmd ON b.BILLID = tmd.BILLID " +
                    "LEFT JOIN TMBILLDISCOUNTREQUEST tmr ON tmd.PARENTID = tmr.ID " +
                    "WHERE b.BUSINESSRECEIVINGID = '" + billId + "'";
            log.info("查询票据明细SQL: " + queryBillItems);
            logService.info(billNo, "查询票据明细SQL: " + queryBillItems);
            
            List<Map<String, Object>> billItems = DBUtil.querySql(queryBillItems);
            log.info("查询到" + (billItems != null ? billItems.size() : 0) + "条票据明细");
            logService.info(billNo, "查询到" + (billItems != null ? billItems.size() : 0) + "条票据明细");
            
            if (billItems == null || billItems.isEmpty()) {
                log.info("业务收款单" + billNo + "没有关联的票据明细，跳过处理");
                logService.info(billNo, "业务收款单" + billNo + "没有关联的票据明细，跳过处理");
                logService.flush(); // 在continue之前刷新日志
                continue;
            }
            
            // 验证必填字段
            boolean hasInvalidFields = false;
            StringBuilder errorMsgBuilder = new StringBuilder();
            
            // 取得银行科目
            String bankTitle = "select FK01 from BFBANKACCOUNTITEMS where parentid = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNT")) + "' and currency = '" + getStringValue(receivingBill.get("CURRENCY")) + "'";
            List<Map<String, Object>> bankTitleList = DBUtil.querySql(bankTitle);
            String hkont = "";
            if (bankTitleList != null && !bankTitleList.isEmpty()) {
                hkont = getStringValue(bankTitleList.get(0).get("FK01"));
            }
            
            // 新增：获取 PAYUNITCODE，它对于当前 receivingBill 下的所有 billItems 都是一样的
            String payUnitCode = getStringValue(receivingBill.get("PAYUNITCODE"));
            
            // 银行账号和银行科目校验
            String bankInfoValidation = validateBankInfo(getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")), hkont, RECEIPT_TYPE_DISCOUNT);
            if (bankInfoValidation != null) {
                hasInvalidFields = true;
                errorMsgBuilder.append("银行信息校验失败: ").append(bankInfoValidation).append(" ");
            }
            
            for (Map<String, Object> billItem : billItems) {
                String validationResult = validateRequiredFields(billItem, RECEIPT_TYPE_DISCOUNT);
                if (validationResult != null) {
                    hasInvalidFields = true;
                    errorMsgBuilder.append("票据号[")
                            .append(getStringValue(billItem.get("BILLNO")))
                            .append("]校验失败: ")
                            .append(validationResult)
                            .append(" ");
                }
            }
            
            if (hasInvalidFields) {
                String errorMsg = errorMsgBuilder.toString();
                log.error("业务收款单" + billNo + "存在字段验证错误: " + errorMsg);
                logService.error(billNo, "业务收款单" + billNo + "存在字段验证错误: " + errorMsg);
                
                // 记录推送失败到数据库
                try {
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_DISCOUNT + "', " +
                                "pjcjms = '" + SCENE_DESC_DISCOUNT + "', " +
                                "account_no = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + PUSH_STATUS_FAILED + "', " +
                                "PUSHLOG = '字段验证错误: " + errorMsg + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + existingLogId + "'";
                        
                        log.info("更新推送日志主表SQL: " + updateLogSql);
                        logService.info(LOG_CODE, "更新推送日志主表SQL: " + updateLogSql);
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE, "更新推送日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 插入新记录
                        String logId = UUID.randomUUID().toString();
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_DISCOUNT + "', " +
                                "'" + SCENE_DESC_DISCOUNT + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + PUSH_STATUS_FAILED + "', " +
                                "'字段验证错误: " + errorMsg + "')";
                        
                        log.info("插入推送日志主表SQL: " + insertLogSql);
                        logService.info(LOG_CODE, "插入推送日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE, "插入推送日志主表结果: " + insertLogResult);
                    }
                } catch (Exception e) {
                    log.error("记录推送结果到数据库异常: " + e.getMessage(), e);
                    logService.error(billNo, "记录推送结果到数据库异常: " + e.getMessage());
                }

                logService.flush(); // 在continue之前刷新日志
                continue; // 跳过推送
            }
            
            // 构建推送SAP的XML
            StringBuffer xmlBuilder = new StringBuffer();
            xmlBuilder.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:goldwind.com:i_gsc:gsc_bill_book\">\n");
            xmlBuilder.append("   <soapenv:Header/>\n");
            xmlBuilder.append("   <soapenv:Body>\n");
            xmlBuilder.append("      <urn:mt_gsc_bill_book>\n");
            xmlBuilder.append("         <iv_msg_flag>GSClOUD</iv_msg_flag>\n");
            xmlBuilder.append("         <is_msg_head>\n");
            xmlBuilder.append("            <MANDT>" + MANDT + "</MANDT>\n");

            xmlBuilder.append("            <GUID>" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "</GUID>\n");
            
            xmlBuilder.append("            <PROXY_ID>" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "</PROXY_ID>\n");
            xmlBuilder.append("            <SYSTEM_ID>GSClOUD</SYSTEM_ID>\n");
            xmlBuilder.append("            <OPERATOR>" + getStringValue(receivingBill.get("USERCODE")) + "</OPERATOR>\n");
            xmlBuilder.append("            <SPRAS>ZH</SPRAS>\n");
            xmlBuilder.append("            <INTERFACE_ID>si_gsc_bill_book</INTERFACE_ID>\n");
            xmlBuilder.append("            <SENDER>GSCloud</SENDER>\n");
            xmlBuilder.append("            <RECIVER>SAP</RECIVER>\n");
            xmlBuilder.append("            <SENDTIME>" + dateTimeFormat.format(new Date()) + "</SENDTIME>\n");
            xmlBuilder.append("         </is_msg_head>\n");
            xmlBuilder.append("         <is_header>\n");
            xmlBuilder.append("            <pjcjbh>" + SCENE_CODE_DISCOUNT + "</pjcjbh>\n");
            xmlBuilder.append("            <pjcjms>" + SCENE_DESC_DISCOUNT + "</pjcjms>\n");
            xmlBuilder.append("            <pjid>" + billId + "</pjid>\n");
            xmlBuilder.append("            <account_no>" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "</account_no>\n");
            xmlBuilder.append("            <hkont>" + hkont + "</hkont>\n");
            xmlBuilder.append("            <budat>" + budatValue + "</budat>\n");
            xmlBuilder.append("            <usecn>" + getStringValue(receivingBill.get("SUMMARY")) + "</usecn>\n");
            xmlBuilder.append("            <user_code>" + getStringValue(receivingBill.get("USERCODE")) + "</user_code>\n");
            xmlBuilder.append("            <user_name>" + getStringValue(receivingBill.get("USERNAME")) + "</user_name>\n");
            xmlBuilder.append("            <apply_date>" + dateFormat.format(new Date()) + "</apply_date>\n");
            xmlBuilder.append("            <bukrs>" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "</bukrs>\n");
            xmlBuilder.append("            <butxt>" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "</butxt>\n");
            xmlBuilder.append("            <reserve_f1></reserve_f1>\n");
            xmlBuilder.append("            <reserve_f2></reserve_f2>\n");
            xmlBuilder.append("            <reserve_f3></reserve_f3>\n");
            xmlBuilder.append("         </is_header>\n");
            
            // 添加票据明细
            int lineNumber = 1;
            for (Map<String, Object> billItem : billItems) {
                xmlBuilder.append("         <it_row>\n");
                xmlBuilder.append("            <line_id>" + lineNumber + "</line_id>\n");
                xmlBuilder.append("            <bill_type>" + ("AC01".equals(getStringValue(billItem.get("BILLTYPE"))) ? "1" : "2") + "</bill_type>\n");
                xmlBuilder.append("            <waers>" + getStringValue(billItem.get("CURRENCY")) + "</waers>\n");
                xmlBuilder.append("            <bill_amount>" + formatBillAmount(billItem.get("BILLAMT")) + "</bill_amount>\n");
                xmlBuilder.append("            <fenum>" + getStringValue(billItem.get("DISCOUNTINTEREST")) + "</fenum>\n");
                
                // 格式化到期日期
                String dueDate = "";
                if (billItem.get("BILLDUEDATE") != null) {
                    try {
                        dueDate = dateFormat.format(billItem.get("BILLDUEDATE"));
                    } catch (Exception e) {
                        log.error("格式化到期日期异常", e);
                    }
                }
                xmlBuilder.append("            <zfbdt>" + dueDate + "</zfbdt>\n");
                xmlBuilder.append("            <busi>" + getStringValue(billItem.get("GSZT")) + "</busi>\n");
                xmlBuilder.append("            <prctr>" + getStringValue(billItem.get("LRZX")) + "</prctr>\n");
                xmlBuilder.append("            <ltext>" + getStringValue(billItem.get("LRZXNAME")) + "</ltext>\n");
                xmlBuilder.append("            <kostl>" + getStringValue(billItem.get("CBZXBH")) + "</kostl>\n");
                xmlBuilder.append("            <cost_name>" + getStringValue(billItem.get("CBZXMC")) + "</cost_name>\n");
                xmlBuilder.append("            <ktnra>" + payUnitCode + "</ktnra>\n");
                xmlBuilder.append("            <bill_no>" + getStringValue(billItem.get("BILLNO")) + "</bill_no>\n");
                xmlBuilder.append("            <sub_bill_start_sn>" + getStringValue(billItem.get("SUBBILLSTARTSN")) + "</sub_bill_start_sn>\n");
                xmlBuilder.append("            <sub_bill_end_sn>" + getStringValue(billItem.get("SUBBILLENDSN")) + "</sub_bill_end_sn>\n");
                xmlBuilder.append("            <tran_rate>" + getStringValue(billItem.get("DISCOUNTRATE")) + "</tran_rate>\n");
                xmlBuilder.append("            <reserve_f1></reserve_f1>\n");
                xmlBuilder.append("            <reserve_f2></reserve_f2>\n");
                xmlBuilder.append("            <reserve_f3></reserve_f3>\n");
                xmlBuilder.append("            <reserve_f4></reserve_f4>\n");
                xmlBuilder.append("            <reserve_f5></reserve_f5>\n");
                xmlBuilder.append("            <reserve_f6></reserve_f6>\n");
                xmlBuilder.append("         </it_row>\n");

                lineNumber++;
            }
            
            xmlBuilder.append("      </urn:mt_gsc_bill_book>\n");
            xmlBuilder.append("   </soapenv:Body>\n");
            xmlBuilder.append("</soapenv:Envelope>");
            
            String xmlData = xmlBuilder.toString();
            log.info("推送SAP的XML数据: " + xmlData);
            logService.info(billNo, "准备推送SAP的XML数据: " + xmlData);
            logService.info(billNo, "准备推送SAP的XML数据，长度: " + xmlData.length());


            logService.info(billNo, "系统配置参数：apiUrl={}, username={}, ssoBaseUrl={}", sapBillPushUrl, sapBillPushUsername, sapBillPushPassword);
    

            // 调用WebService推送数据到SAP
            try {
                String result = webServiceRequest(logService, billNo, sapBillPushUrl, "", xmlData, sapBillPushUsername, sapBillPushPassword, true);
                
                log.info("推送SAP结果: " + result);
                logService.info(billNo, "推送SAP结果: " + result);
                
                // 处理推送结果
                String pushStatus;
                String pushLog;
                
                // 判断是否成功推送
                // 情况1: 结果为空
                // 情况2: 返回的是空的SOAP信封，没有错误信息
                boolean isEmptySoapEnvelope = result != null && 
                    (result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header\\s*/>\\s*<SOAP:Body\\s*/>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header>\\s*</SOAP:Header>\\s*<SOAP:Body>\\s*</SOAP:Body>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header/>\\s*<SOAP:Body/>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<[^:]*:Envelope[^>]*>\\s*<[^:]*:Header\\s*/>\\s*<[^:]*:Body\\s*/>\\s*</[^:]*:Envelope>.*"));
                
                if (result == null || result.trim().isEmpty() || isEmptySoapEnvelope) {
                    // 已推送
                    pushStatus = PUSH_STATUS_PUSHED; // 修改点：原为 PUSH_STATUS_SUCCESS (Line 490)
                    pushLog = "已推送";
                    log.info("业务收款单" + billNo + "已推送至SAP");
                    logService.info(billNo, "业务收款单" + billNo + "已推送至SAP");
                } else {
                    // 推送失败
                    pushStatus = PUSH_STATUS_FAILED;
                    pushLog = "推送失败: " + result;
                    log.error("业务收款单" + billNo + "推送失败: " + result);
                    logService.error(billNo, "业务收款单" + billNo + "推送失败: " + result);
                }
                
                // 记录推送结果到数据库
                try {
                    // 根据是否需要更新确定记录ID
                    String logId = needToUpdate ? existingLogId : UUID.randomUUID().toString();
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_DISCOUNT + "', " +
                                "pjcjms = '" + SCENE_DESC_DISCOUNT + "', " +
                                "account_no = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + pushStatus + "', " +
                                "PUSHLOG = '" + pushLog + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + logId + "'";
                        
                        log.info("更新推送日志主表SQL: " + updateLogSql);
                        logService.info(LOG_CODE, "更新推送日志主表SQL: " + updateLogSql);
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE, "更新推送日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 插入新记录
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_DISCOUNT + "', " +
                                "'" + SCENE_DESC_DISCOUNT + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + pushStatus + "', " +
                                "'" + pushLog + "')";
                        
                        log.info("插入推送日志主表SQL: " + insertLogSql);
                        logService.info(LOG_CODE, "插入推送日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE, "插入推送日志主表结果: " + insertLogResult);
                    }
                    
                    // 插入子表数据
                    for (int i = 0; i < billItems.size(); i++) {
                        Map<String, Object> billItem = billItems.get(i);
                        String itemId = UUID.randomUUID().toString();
                        
                        String insertItemSql = "INSERT INTO JTGKSAPPUSHBILLLOGITEM (ID, PARENTID, bill_type, waers, " +
                                "bill_amount, fenum, zfbdt, busi, prctr, ltext, kostl, cost_name, ktnra, " +
                                "bill_no, sub_bill_start_sn, sub_bill_end_sn, tran_rate, " +
                                "reserve_f1, reserve_f2, reserve_f3, reserve_f4, reserve_f5, reserve_f6, " +
                                "TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY) VALUES (";
                        
                        String billType = "AC01".equals(getStringValue(billItem.get("BILLTYPE"))) ? "1" : "2";
                        String dueDate = "";
                        if (billItem.get("BILLDUEDATE") != null) {
                            try {
                                dueDate = dateFormat.format(billItem.get("BILLDUEDATE"));
                            } catch (Exception e) {
                                log.error("格式化到期日期异常", e);
                            }
                        }
                        
                        insertItemSql += "'" + itemId + "', " +
                                "'" + logId + "', " +
                                "'" + billType + "', " +
                                "'" + getStringValue(billItem.get("CURRENCY")) + "', " +
                                "'" + formatBillAmount(billItem.get("BILLAMT")) + "', " +
                                "'" + getStringValue(billItem.get("DISCOUNTINTEREST")) + "', " +
                                "'" + dueDate + "', " +
                                "'" + getStringValue(billItem.get("GSZT")) + "', " +
                                "'" + getStringValue(billItem.get("LRZX")) + "', " +
                                "'" + getStringValue(billItem.get("LRZXNAME")) + "', " +
                                "'" + getStringValue(billItem.get("CBZXBH")) + "', " +
                                "'" + getStringValue(billItem.get("CBZXMC")) + "', " +
                                "'" + payUnitCode + "', " + // ktnra
                                "'" + getStringValue(billItem.get("BILLNO")) + "', " +
                                "'" + getStringValue(billItem.get("SUBBILLSTARTSN")) + "', " +
                                "'" + getStringValue(billItem.get("SUBBILLENDSN")) + "', " +
                                "'" + getStringValue(billItem.get("DISCOUNTRATE")) + "', " +
                                "'', '', '', '', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "')";
                        
                        log.info("插入推送日志子表SQL " + (i+1) + "/" + billItems.size());
                        logService.info(LOG_CODE, "插入推送日志子表SQL " + (i+1) + "/" + billItems.size() + " 长度: " + insertItemSql.length());
                        
                        int insertItemResult = DBUtil.executeUpdateSQL(insertItemSql);
                        log.info("插入推送日志子表结果: " + insertItemResult);
                        logService.info(LOG_CODE, "插入推送日志子表结果: " + insertItemResult);
                    }
                    
                } catch (Exception e) {
                    log.error("记录推送结果到数据库异常: " + e.getMessage(), e);
                    logService.error(billNo, "记录推送结果到数据库异常: " + e.getMessage());
                }
                
            } catch (Exception e) {
                log.error("推送SAP异常: " + e.getMessage(), e);
                logService.error(billNo, "推送SAP异常: " + e.getMessage());
                
                // 记录推送异常到数据库
                try {
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_DISCOUNT + "', " +
                                "pjcjms = '" + SCENE_DESC_DISCOUNT + "', " +
                                "account_no = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + PUSH_STATUS_FAILED + "', " +
                                "PUSHLOG = '推送异常: " + e.getMessage() + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + existingLogId + "'";
                        
                        log.info("更新推送异常日志主表SQL");
                        logService.info(LOG_CODE, "更新推送异常日志主表SQL");
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送异常日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE, "更新推送异常日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 生成主表ID
                        String logId = UUID.randomUUID().toString();
                        
                        // 插入主表数据
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_DISCOUNT + "', " +
                                "'" + SCENE_DESC_DISCOUNT + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + PUSH_STATUS_FAILED + "', " +
                                "'推送异常: " + e.getMessage() + "')";
                        
                        log.info("插入推送异常日志主表SQL");
                        logService.info(LOG_CODE, "插入推送异常日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送异常日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE, "插入推送异常日志主表结果: " + insertLogResult);
                    }
                    
                } catch (Exception ex) {
                    log.error("记录推送异常到数据库异常: " + ex.getMessage(), ex);
                    logService.error(billNo, "记录推送异常到数据库异常: " + ex.getMessage());
                }
            }

            // 为每个billNo单独刷新日志
            logService.flush();
        }

        log.info("应收票据贴现到账票据推送SAP处理完成");
        logService.info(LOG_CODE, "应收票据贴现到账票据推送SAP处理完成");
    }
    
    /**
     * WebService请求方法
     * @param logService 日志服务
     * @param logCode 日志代码
     * @param url 请求URL
     * @param soapAction SOAP action
     * @param data 请求数据
     * @param username 用户名
     * @param password 密码
     * @param authFlag 是否需要认证
     * @return 响应结果
     */
    public static String webServiceRequest(LogService logService, String logCode, String url, String soapAction, String data, 
                                          String username, String password, boolean authFlag) {
        String xmlString = null;
        try {
            URL realURL = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) realURL.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");
            connection.setRequestProperty("SOAPAction", soapAction);
            connection.setRequestMethod("POST");
            if (authFlag) {
                String authString = username + ":" + password;
                String authStringEncoded = Base64.getEncoder().encodeToString(authString.getBytes());
                String basicAuth = "Basic " + authStringEncoded;
                connection.setRequestProperty("Authorization", basicAuth);
            }
            DataOutputStream printOut = new DataOutputStream(connection.getOutputStream());
            printOut.write(data.getBytes("UTF-8"));
            printOut.flush();
            printOut.close();

            // 从连接的输入流中取得回执信息
            InputStream inputStream = connection.getInputStream();
            InputStreamReader isr = new InputStreamReader(inputStream, "UTF-8");
            BufferedReader bufreader = new BufferedReader(isr);

            StringBuilder xmlStringBuilder = new StringBuilder();
            int c;
            while ((c = bufreader.read()) != -1) {
                xmlStringBuilder.append((char) c);
            }
            xmlString = xmlStringBuilder.toString();
            logService.info(logCode, "请求" + url + "返回结果：" + xmlString);
            isr.close();
        } catch (Throwable ex) {
            logService.error(logCode, "异常信息：" + ex.getMessage());
            log.error("异常信息：", ex);
            xmlString = ex.getMessage();
        }
        return xmlString;
    }
    
    /**
     * 获取对象的字符串值，避免空指针异常
     * @param obj 对象
     * @return 字符串值
     */
    private String getStringValue(Object obj) {
        return obj == null ? "" : String.valueOf(obj);
    }

    /**
     * 格式化金额为两位小数
     * @param obj 金额对象
     * @return 格式化后的金额字符串
     */
    private String formatBillAmount(Object obj) {
        if (obj == null) {
            return "0.00";
        }
        
        try {
            // 尝试转换为double
            double amount;
            if (obj instanceof Number) {
                amount = ((Number) obj).doubleValue();
            } else {
                amount = Double.parseDouble(String.valueOf(obj));
            }
            
            // 格式化为两位小数
            DecimalFormat df = new DecimalFormat("#0.00");
            return df.format(amount);
        } catch (NumberFormatException e) {
            // 如果转换失败，返回原值
            return String.valueOf(obj);
        }
    }

    /**
     * 验证必填字段
     * @param billItem 票据明细
     * @param billType 票据类型 (RECEIPT_TYPE_DISCOUNT/RECEIPT_TYPE_COLLECTION/RECEIPT_TYPE_PAYABLE)
     * @return 验证结果，如果通过返回null，否则返回错误信息
     */
    private String validateRequiredFields(Map<String, Object> billItem, String billType) {
        StringBuilder errorMsg = new StringBuilder();
        
        if (RECEIPT_TYPE_DISCOUNT.equals(billType)) {
            // 利息 - 贴现场景必填
            if (isEmpty(getStringValue(billItem.get("DISCOUNTINTEREST")))) {
                errorMsg.append("票据利息(fenum)不能为空; ");
            }
            
            // 成本中心编号 - 贴现场景必填
            if (isEmpty(getStringValue(billItem.get("CBZXBH")))) {
                errorMsg.append("成本中心编号(kostl)不能为空; ");
            }
            
            // 成本中心名称 - 贴现场景必填
            if (isEmpty(getStringValue(billItem.get("CBZXMC")))) {
                errorMsg.append("成本中心名称(cost_name)不能为空; ");
            }
            
            // 贴现利率 - 贴现业务必填
            if (isEmpty(getStringValue(billItem.get("DISCOUNTRATE")))) {
                errorMsg.append("贴现利率(tran_rate)不能为空; ");
            }
        }
        
        return errorMsg.length() > 0 ? errorMsg.toString() : null;
    }
    
    /**
     * 校验银行账号和银行科目
     * @param accountNo 银行账号
     * @param bankSubject 银行科目
     * @param billType 票据类型
     * @return 校验结果，通过返回null
     */
    private String validateBankInfo(String accountNo, String bankSubject, String billType) {
        StringBuilder errorMsg = new StringBuilder();
        
        // 银行科目 - 托收/贴现/到期兑付 场景必填
        if (isEmpty(bankSubject)) {
            errorMsg.append("银行科目(hkont)不能为空; ");
        }
        
        // 银行账号 - 托收或贴现场景必填
        if ((RECEIPT_TYPE_DISCOUNT.equals(billType) || RECEIPT_TYPE_COLLECTION.equals(billType)) 
                && isEmpty(accountNo)) {
            errorMsg.append("银行账号(account_no)不能为空; ");
        }
        
        return errorMsg.length() > 0 ? errorMsg.toString() : null;
    }
    
    /**
     * 判断字符串是否为空
     * @param str 字符串
     * @return 是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 应收票据托收到账票据推送sap完成凭证推送
     */
    public void pushCollectionBillToSap() {
        String jobId = "5ff8ad21-3b67-4c89-a3e4-ce19f25a7de1";
        String jobName = "应收票据托收到账票据推送SAP";
        String lockId;
        
        try {
            // 获取分布式锁
            String moduleId = "JfskReceivableBillCollectionSap";
            String funcId = "pushCollectionBillToSap";
            String categoryId = "String";
            String dataID = "jtgk-Receivable-Bill-Collection-Sap";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, 
                    new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, 
                            LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                log.error(jobName + "加锁失败");
                return;
            }
            
            lockId = lockResult.getLockId();
            log.info(jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发生异常：" + ex.toString());
            log.error(jobName + "加锁过程发生异常：", ex);
            return;
        }

        logService.init(LOG_CODE_COLLECTION);
        try {
            processCollectionBillAndPushToSap(logService);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, ex.toString());
            log.error("应收票据托收到账票据推送SAP过程发生异常：", ex);
            logService.error(LOG_CODE_COLLECTION, "应收票据托收到账票据推送SAP过程发生异常：" + ExceptionUtils.getStackTrace(ex));
        }
        
        logService.flush();
        
        try {
            lockService.removeLock(lockId);
            log.info(jobName + "已解锁");
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
            log.error(jobName + "解锁过程发生异常：", ex);
        }
    }

    /**
     * 处理应收票据托收到账并推送SAP
     * @param logService 日志服务
     */
    private void processCollectionBillAndPushToSap(LogService logService) {
        log.info("开始处理应收票据托收到账并推送SAP");
        logService.info(LOG_CODE_COLLECTION, "开始处理应收票据托收到账并推送SAP");

        logService.info(LOG_CODE_COLLECTION, "开始处理应收票据托收到SAP相关参数");
        String selectSql = "select CODE, TXT01, TXT02, BIGTXT01 from IDD_DATADICTIONARY where CATEGORYID='b9badc01-6db5-5b10-5813-5311071bbccf' and TXT03='pjywpz'";
        logService.info(LOG_CODE_COLLECTION, "执行SQL：{}", selectSql);
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectSql);
        logService.info(LOG_CODE_COLLECTION, "查询结果：{}", JSON.toJSONString(rowsOfBizSys));
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            logService.error(LOG_CODE_COLLECTION, "未找到业务系统配置");
            logService.flush();
            return;
        }

        // 环境配置
        String MANDT = rowsOfBizSys.get(0).get("CODE").toString();
        String sapBillPushUsername = rowsOfBizSys.get(0).get("TXT01").toString();
        String sapBillPushPassword = rowsOfBizSys.get(0).get("TXT02").toString();
        String sapBillPushUrl = rowsOfBizSys.get(0).get("BIGTXT01").toString();
        
        
        // 查询符合条件的业务收款单 - 托收类型
        String querySql = "SELECT distinct br.ID, br.DOCNO, br.RECEIVINGACCOUNT as RECEIVINGACCOUNT, br.RECEIVINGACCOUNTNO as RECEIVINGACCOUNTNO, bi.NAME_CHS AS BANKNAME, br.CURRENCY as CURRENCY, " +
                "br.RECEIVINGUNIT, org.NAME_CHS AS RECEIVINGUNITNAME, org.CODE AS RECEIVINGUNITCODE, payOrg.NAME_CHS AS PAYUNITNAME, payOrg.CODE AS PAYUNITCODE, " +
                "br.TIMESTAMPS_CREATEDON, user_info.NAME_CHS AS USERNAME, " +
                "br.SUMMARY AS SUMMARY, user_info.code as USERCODE, " +
                "jspl.ID AS existingLogId, jspl.PUSHSTATUS AS existingPushStatus " + // 新增字段
                "FROM BPBusinessReceiving br " +
                "INNER JOIN ZJRLJL rl ON rl.BZDNM = br.ID " +
                "INNER JOIN ZJRLLX lx ON rl.RLLX = lx.ID AND lx.lxbh = '" + RECEIPT_TYPE_COLLECTION + "' " +
                "LEFT JOIN JTGKSAPPUSHBILLLOG jspl ON br.ID = jspl.pjid " + // 新增JOIN
                "LEFT JOIN BFADMINORGANIZATION org ON br.RECEIVINGUNIT = org.ID " +
                "LEFT JOIN BFPARTNER payOrg ON br.PAYUNIT = payOrg.ID " +
                "LEFT JOIN GSPUser user_info ON br.PRODUCERID = user_info.ID " +
                "LEFT JOIN BFBANKACCOUNTS ba ON br.RECEIVINGACCOUNT = ba.ID " +
                "LEFT JOIN BFBANK bi ON ba.BANK = bi.ID " +
                "WHERE br.docstatus = 3 " +
                "AND (jspl.ID IS NULL OR jspl.PUSHSTATUS NOT IN ('" + PUSH_STATUS_PUSHED + "', '" + PUSH_STATUS_SUCCESS + "')) " + // 新增条件
                "ORDER BY br.TIMESTAMPS_CREATEDON DESC";

        log.info("查询SQL: " + querySql);
        logService.info(LOG_CODE_COLLECTION, "查询SQL: " + querySql);
        
        List<Map<String, Object>> receivingBills = DBUtil.querySql(querySql);
        log.info("查询结果: " + JSON.toJSONString(receivingBills));
        logService.info(LOG_CODE_COLLECTION, "查询到" + (receivingBills != null ? receivingBills.size() : 0) + "条业务收款单");

        if (receivingBills == null || receivingBills.isEmpty()) {
            log.info("没有查询到符合条件的业务收款单");
            logService.info(LOG_CODE_COLLECTION, "没有查询到符合条件的业务收款单");
            return;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        
        // 循环处理每一个业务收款单
        for (Map<String, Object> receivingBill : receivingBills) {
            String billId = (String) receivingBill.get("ID");
            String billNo = (String) receivingBill.get("DOCNO");

            // 为每个billNo单独初始化日志
            logService.init(billNo);

            // 获取业务发生日BUDAT并格式化为yyyyMMdd
            String businessDateSql = "SELECT TRANSACTIONDATE FROM BPBIZBANKTRANSDETAILS WHERE PARENTID = '" + billId + "'";
            List<Map<String, Object>> businessDateList = DBUtil.querySql(businessDateSql);
            String budatValue = ""; // 用于SAP XML和数据库日志。确保 dateFormat 在此作用域内已定义为 new SimpleDateFormat("yyyyMMdd")

            if (businessDateList != null && !businessDateList.isEmpty()) {
                Object transactionDateObj = businessDateList.get(0).get("TRANSACTIONDATE");
                if (transactionDateObj != null) {
                    if (transactionDateObj instanceof java.util.Date) { // 包括 java.sql.Date 和 java.sql.Timestamp
                        try {
                            // dateFormat 实例应为 new SimpleDateFormat("yyyyMMdd")
                            budatValue = dateFormat.format((java.util.Date) transactionDateObj);
                        } catch (Exception e) {
                            log.error("Error formatting TRANSACTIONDATE (Date object) for billId " + billId + " in " + LOG_CODE_COLLECTION + ": " + e.getMessage(), e);
                            logService.error(LOG_CODE_COLLECTION, "Error formatting TRANSACTIONDATE (Date object) for billId " + billId + ": " + e.getMessage());
                            // budatValue 保持为 ""
                        }
                    } else {
                        // 如果不是 Date 对象，尝试将其作为字符串解析
                        String transactionDateStr = transactionDateObj.toString().trim();
                        if (!transactionDateStr.isEmpty()) {
                            try {
                                Date parsedDate = null;
                                // 尝试常见的日期时间格式，如 "yyyy-MM-dd HH:mm:ss.S", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"
                                if (transactionDateStr.matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}(\\.\\d+)?$")) {
                                    parsedDate = new SimpleDateFormat(transactionDateStr.contains(".") ? "yyyy-MM-dd HH:mm:ss.S" : "yyyy-MM-dd HH:mm:ss").parse(transactionDateStr);
                                } else if (transactionDateStr.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                                    parsedDate = new SimpleDateFormat("yyyy-MM-dd").parse(transactionDateStr);
                                }

                                if (parsedDate != null) {
                                    budatValue = dateFormat.format(parsedDate);
                                } else {
                                    log.warn("TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_COLLECTION + " did not match known date/timestamp patterns for parsing.");
                                    logService.warn(LOG_CODE_COLLECTION, "TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_COLLECTION + " did not match known date/timestamp patterns for parsing.");
                                }
                            } catch (java.text.ParseException pe) {
                                log.warn("Could not parse TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_COLLECTION + ". Error: " + pe.getMessage());
                                logService.warn(LOG_CODE_COLLECTION, "Could not parse TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_COLLECTION + ". Error: " + pe.getMessage());
                                // budatValue 保持为 ""
                            }
                        }
                    }
                }
            }
            // 原 businessDate 变量现在由 budatValue 替代。
            
            log.info("处理业务收款单: " + billNo);
            logService.info(billNo, "处理业务收款单: " + billNo);
            
            // 定义变量，存储是否需要更新记录、已有记录的ID
            boolean needToUpdate = false;
            String existingLogId = getStringValue(receivingBill.get("EXISTINGLOGID"));
            String existingPushStatus = getStringValue(receivingBill.get("EXISTINGPUSHSTATUS"));
            
            if (existingLogId != null && !existingLogId.isEmpty()) {
                log.info("业务收款单" + billNo + "之前推送状态为 " + existingPushStatus + " (LogID: " + existingLogId + ")，将重新推送并更新记录。");
                logService.info(billNo, "业务收款单" + billNo + "之前推送状态为 " + existingPushStatus + " (LogID: " + existingLogId + ")，将重新推送并更新记录。");
                needToUpdate = true;
            } else {
                 log.info("业务收款单" + billNo + "为新票据或之前未成功记录，将进行推送尝试。");
                 logService.info(billNo, "业务收款单" + billNo + "为新票据或之前未成功记录，将进行推送尝试。");
            }
            
            // 查询收款单下的票据明细
            String queryBillItems = "SELECT b.ID as ID, b.BILLID AS BILLID, b.BILLNO, b.BILLTYPE, b.BILLAMT, b.DISCOUNTINTEREST, b.BILLDUEDATE, b.DISCOUNTRATE, c.CODE AS CURRENCY, tmb.TXT11 as GSZT, tmb.TXT03 as LRZX, tmb.TXT04 as LRZXNAME, " +
                    "b.SUBBILLSTARTSN, b.SUBBILLENDSN " +
                    "FROM BPBIZRCVBILLITEM b " +
                    "LEFT JOIN BFCURRENCY c ON b.CURRENCY = c.ID " +
                    "LEFT JOIN TMBILLRECEIVABLEINVENTORY tmb ON b.BILLID = tmb.ID " +
                    "WHERE b.BUSINESSRECEIVINGID = '" + billId + "'";
            log.info("查询票据明细SQL: " + queryBillItems);
            logService.info(billNo, "查询票据明细SQL: " + queryBillItems);
            
            List<Map<String, Object>> billItems = DBUtil.querySql(queryBillItems);
            log.info("查询到" + (billItems != null ? billItems.size() : 0) + "条票据明细");
            logService.info(billNo, "查询到" + (billItems != null ? billItems.size() : 0) + "条票据明细");
            
            if (billItems == null || billItems.isEmpty()) {
                log.info("业务收款单" + billNo + "没有关联的票据明细，跳过处理");
                logService.info(billNo, "业务收款单" + billNo + "没有关联的票据明细，跳过处理");
                logService.flush(); // 在continue之前刷新日志
                continue;
            }
            
            // 验证必填字段
            boolean hasInvalidFields = false;
            StringBuilder errorMsgBuilder = new StringBuilder();
            
            // 取得银行科目
            String bankTitle = "select FK01 from BFBANKACCOUNTITEMS where parentid = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNT")) + "' and currency = '" + getStringValue(receivingBill.get("CURRENCY")) + "'";
            List<Map<String, Object>> bankTitleList = DBUtil.querySql(bankTitle);
            String hkont = "";
            if (bankTitleList != null && !bankTitleList.isEmpty()) {
                hkont = getStringValue(bankTitleList.get(0).get("FK01"));
            }
            
            // 新增：获取 PAYUNITCODE，它对于当前 receivingBill 下的所有 billItems 都是一样的
            String payUnitCode = getStringValue(receivingBill.get("PAYUNITCODE"));
            
            // 银行账号和银行科目校验
            String bankInfoValidation = validateBankInfo(getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")), hkont, RECEIPT_TYPE_COLLECTION);
            if (bankInfoValidation != null) {
                hasInvalidFields = true;
                errorMsgBuilder.append("银行信息校验失败: ").append(bankInfoValidation).append(" ");
            }
            
            for (Map<String, Object> billItem : billItems) {
                String validationResult = validateRequiredFields(billItem, RECEIPT_TYPE_COLLECTION);
                if (validationResult != null) {
                    hasInvalidFields = true;
                    errorMsgBuilder.append("票据号[")
                            .append(getStringValue(billItem.get("BILLNO")))
                            .append("]校验失败: ")
                            .append(validationResult)
                            .append(" ");
                }
            }
            
            if (hasInvalidFields) {
                String errorMsg = errorMsgBuilder.toString();
                log.error("业务收款单" + billNo + "存在字段验证错误: " + errorMsg);
                logService.error(billNo, "业务收款单" + billNo + "存在字段验证错误: " + errorMsg);
                
                // 记录推送失败到数据库
                try {
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);
                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_COLLECTION + "', " +
                                "pjcjms = '" + SCENE_DESC_COLLECTION + "', " +
                                "account_no = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + PUSH_STATUS_FAILED + "', " +
                                "PUSHLOG = '字段验证错误: " + errorMsg + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + existingLogId + "'";
                        
                        log.info("更新推送日志主表SQL: " + updateLogSql);
                        logService.info(LOG_CODE_COLLECTION, "更新推送日志主表SQL: " + updateLogSql);
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE_COLLECTION, "更新推送日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE_COLLECTION, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE_COLLECTION, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 插入新记录
                        String logId = UUID.randomUUID().toString();
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_COLLECTION + "', " +
                                "'" + SCENE_DESC_COLLECTION + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + PUSH_STATUS_FAILED + "', " +
                                "'字段验证错误: " + errorMsg + "')";
                        
                        log.info("插入推送日志主表SQL: " + insertLogSql);
                        logService.info(LOG_CODE_COLLECTION, "插入推送日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE_COLLECTION, "插入推送日志主表结果: " + insertLogResult);
                    }
                } catch (Exception e) {
                    log.error("记录推送结果到数据库异常: " + e.getMessage(), e);
                    logService.error(billNo, "记录推送结果到数据库异常: " + e.getMessage());
                }

                logService.flush(); // 在continue之前刷新日志
                continue; // 跳过推送
            }
            
            // 构建推送SAP的XML
            StringBuffer xmlBuilder = new StringBuffer();
            xmlBuilder.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:goldwind.com:i_gsc:gsc_bill_book\">\n");
            xmlBuilder.append("   <soapenv:Header/>\n");
            xmlBuilder.append("   <soapenv:Body>\n");
            xmlBuilder.append("      <urn:mt_gsc_bill_book>\n");
            xmlBuilder.append("         <iv_msg_flag>GSClOUD</iv_msg_flag>\n");
            xmlBuilder.append("         <is_msg_head>\n");
            xmlBuilder.append("            <MANDT>" + MANDT + "</MANDT>\n");

            xmlBuilder.append("            <GUID>" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "</GUID>\n");
            
            xmlBuilder.append("            <PROXY_ID>" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "</PROXY_ID>\n");
            xmlBuilder.append("            <SYSTEM_ID>GSClOUD</SYSTEM_ID>\n");
            xmlBuilder.append("            <OPERATOR>" + getStringValue(receivingBill.get("USERCODE")) + "</OPERATOR>\n");
            xmlBuilder.append("            <SPRAS>ZH</SPRAS>\n");
            xmlBuilder.append("            <INTERFACE_ID>si_gsc_bill_book</INTERFACE_ID>\n");
            xmlBuilder.append("            <SENDER>GSCloud</SENDER>\n");
            xmlBuilder.append("            <RECIVER>SAP</RECIVER>\n");
            xmlBuilder.append("            <SENDTIME>" + dateTimeFormat.format(new Date()) + "</SENDTIME>\n");
            xmlBuilder.append("         </is_msg_head>\n");
            xmlBuilder.append("         <is_header>\n");
            xmlBuilder.append("            <pjcjbh>" + SCENE_CODE_COLLECTION + "</pjcjbh>\n");
            xmlBuilder.append("            <pjcjms>" + SCENE_DESC_COLLECTION + "</pjcjms>\n");
            xmlBuilder.append("            <pjid>" + billId + "</pjid>\n");
            xmlBuilder.append("            <account_no>" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "</account_no>\n");
            xmlBuilder.append("            <hkont>" + hkont + "</hkont>\n");
            xmlBuilder.append("            <budat>" + budatValue + "</budat>\n");
            xmlBuilder.append("            <usecn>" + getStringValue(receivingBill.get("SUMMARY")) + "</usecn>\n");
            xmlBuilder.append("            <user_code>" + getStringValue(receivingBill.get("USERCODE")) + "</user_code>\n");
            xmlBuilder.append("            <user_name>" + getStringValue(receivingBill.get("USERNAME")) + "</user_name>\n");
            xmlBuilder.append("            <apply_date>" + dateFormat.format(new Date()) + "</apply_date>\n");
            xmlBuilder.append("            <bukrs>" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "</bukrs>\n");
            xmlBuilder.append("            <butxt>" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "</butxt>\n");
            xmlBuilder.append("            <reserve_f1></reserve_f1>\n");
            xmlBuilder.append("            <reserve_f2></reserve_f2>\n");
            xmlBuilder.append("            <reserve_f3></reserve_f3>\n");
            xmlBuilder.append("         </is_header>\n");
            
            // 添加票据明细
            int lineNumber = 1;
            for (Map<String, Object> billItem : billItems) {
                xmlBuilder.append("         <it_row>\n");
                xmlBuilder.append("            <line_id>" + lineNumber + "</line_id>\n");
                xmlBuilder.append("            <bill_type>" + ("AC01".equals(getStringValue(billItem.get("BILLTYPE"))) ? "1" : "2") + "</bill_type>\n");
                xmlBuilder.append("            <waers>" + getStringValue(billItem.get("CURRENCY")) + "</waers>\n");
                xmlBuilder.append("            <bill_amount>" + formatBillAmount(billItem.get("BILLAMT")) + "</bill_amount>\n");
                xmlBuilder.append("            <fenum>" + getStringValue(billItem.get("DISCOUNTINTEREST")) + "</fenum>\n");
                
                // 格式化到期日期
                String dueDate = "";
                if (billItem.get("BILLDUEDATE") != null) {
                    try {
                        dueDate = dateFormat.format(billItem.get("BILLDUEDATE"));
                    } catch (Exception e) {
                        log.error("格式化到期日期异常", e);
                    }
                }
                xmlBuilder.append("            <zfbdt>" + dueDate + "</zfbdt>\n");
                xmlBuilder.append("            <busi>" + getStringValue(billItem.get("GSZT")) + "</busi>\n");
                xmlBuilder.append("            <prctr>" + getStringValue(billItem.get("LRZX")) + "</prctr>\n");
                xmlBuilder.append("            <ltext>" + getStringValue(billItem.get("LRZXNAME")) + "</ltext>\n");
                xmlBuilder.append("            <kostl></kostl>\n");
                xmlBuilder.append("            <cost_name></cost_name>\n");
                xmlBuilder.append("            <ktnra>" + payUnitCode + "</ktnra>\n"); 
                xmlBuilder.append("            <bill_no>" + getStringValue(billItem.get("BILLNO")) + "</bill_no>\n");
                xmlBuilder.append("            <sub_bill_start_sn>" + getStringValue(billItem.get("SUBBILLSTARTSN")) + "</sub_bill_start_sn>\n");
                xmlBuilder.append("            <sub_bill_end_sn>" + getStringValue(billItem.get("SUBBILLENDSN")) + "</sub_bill_end_sn>\n");
                xmlBuilder.append("            <tran_rate>" + getStringValue(billItem.get("DISCOUNTRATE")) + "</tran_rate>\n");
                xmlBuilder.append("            <reserve_f1></reserve_f1>\n");
                xmlBuilder.append("            <reserve_f2></reserve_f2>\n");
                xmlBuilder.append("            <reserve_f3></reserve_f3>\n");
                xmlBuilder.append("            <reserve_f4></reserve_f4>\n");
                xmlBuilder.append("            <reserve_f5></reserve_f5>\n");
                xmlBuilder.append("            <reserve_f6></reserve_f6>\n");
                xmlBuilder.append("         </it_row>\n");

                lineNumber++;
            }
            
            xmlBuilder.append("      </urn:mt_gsc_bill_book>\n");
            xmlBuilder.append("   </soapenv:Body>\n");
            xmlBuilder.append("</soapenv:Envelope>");
            
            String xmlData = xmlBuilder.toString();
            log.info("推送SAP的XML数据: " + xmlData);
            logService.info(billNo, "准备推送SAP的XML数据: " + xmlData);
            logService.info(billNo, "准备推送SAP的XML数据，长度: " + xmlData.length());

            logService.info(billNo, "系统配置参数：apiUrl={}, username={}, ssoBaseUrl={}", sapBillPushUrl, sapBillPushUsername, sapBillPushPassword);
    
            // 调用WebService推送数据到SAP
            try {
                String result = webServiceRequest(logService, billNo, sapBillPushUrl, "", xmlData, sapBillPushUsername, sapBillPushPassword, true);
                
                log.info("推送SAP结果: " + result);
                logService.info(billNo, "推送SAP结果: " + result);
                
                // 处理推送结果
                String pushStatus;
                String pushLog;
                
                // 判断是否成功推送
                // 情况1: 结果为空
                // 情况2: 返回的是空的SOAP信封，没有错误信息
                boolean isEmptySoapEnvelope = result != null && 
                    (result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header\\s*/>\\s*<SOAP:Body\\s*/>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header>\\s*</SOAP:Header>\\s*<SOAP:Body>\\s*</SOAP:Body>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header/>\\s*<SOAP:Body/>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<[^:]*:Envelope[^>]*>\\s*<[^:]*:Header\\s*/>\\s*<[^:]*:Body\\s*/>\\s*</[^:]*:Envelope>.*"));
                
                if (result == null || result.trim().isEmpty() || isEmptySoapEnvelope) {
                    // 已推送
                    pushStatus = PUSH_STATUS_PUSHED; // 修改点：原为 PUSH_STATUS_SUCCESS
                    pushLog = "已推送";
                    log.info("业务收款单" + billNo + "已推送至SAP");
                    logService.info(billNo, "业务收款单" + billNo + "已推送至SAP");
                } else {
                    // 推送失败
                    pushStatus = PUSH_STATUS_FAILED;
                    pushLog = "推送失败: " + result;
                    log.error("业务收款单" + billNo + "推送失败: " + result);
                    logService.error(billNo, "业务收款单" + billNo + "推送失败: " + result);
                }
                
                // 记录推送结果到数据库
                try {
                    // 根据是否需要更新确定记录ID
                    String logId = needToUpdate ? existingLogId : UUID.randomUUID().toString();
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_COLLECTION + "', " +
                                "pjcjms = '" + SCENE_DESC_COLLECTION + "', " +
                                "account_no = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + pushStatus + "', " +
                                "PUSHLOG = '" + pushLog + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + logId + "'";
                        
                        log.info("更新推送日志主表SQL: " + updateLogSql);
                        logService.info(LOG_CODE_COLLECTION, "更新推送日志主表SQL: " + updateLogSql);
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE_COLLECTION, "更新推送日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + logId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE_COLLECTION, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE_COLLECTION, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 插入新记录
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_COLLECTION + "', " +
                                "'" + SCENE_DESC_COLLECTION + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + pushStatus + "', " +
                                "'" + pushLog + "')";
                        
                        log.info("插入推送日志主表SQL: " + insertLogSql);
                        logService.info(LOG_CODE_COLLECTION, "插入推送日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE_COLLECTION, "插入推送日志主表结果: " + insertLogResult);
                    }
                    
                    // 插入子表数据
                    for (int i = 0; i < billItems.size(); i++) {
                        Map<String, Object> billItem = billItems.get(i);
                        String itemId = UUID.randomUUID().toString();
                        
                        String insertItemSql = "INSERT INTO JTGKSAPPUSHBILLLOGITEM (ID, PARENTID, bill_type, waers, " +
                                "bill_amount, fenum, zfbdt, busi, prctr, ltext, kostl, cost_name, ktnra, " +
                                "bill_no, sub_bill_start_sn, sub_bill_end_sn, tran_rate, " +
                                "reserve_f1, reserve_f2, reserve_f3, reserve_f4, reserve_f5, reserve_f6, " +
                                "TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY) VALUES (";
                        
                        String billType = "AC01".equals(getStringValue(billItem.get("BILLTYPE"))) ? "1" : "2";
                        String dueDate = "";
                        if (billItem.get("BILLDUEDATE") != null) {
                            try {
                                dueDate = dateFormat.format(billItem.get("BILLDUEDATE"));
                            } catch (Exception e) {
                                log.error("格式化到期日期异常", e);
                            }
                        }
                        
                        insertItemSql += "'" + itemId + "', " +
                                "'" + logId + "', " +
                                "'" + billType + "', " +
                                "'" + getStringValue(billItem.get("CURRENCY")) + "', " +
                                "'" + formatBillAmount(billItem.get("BILLAMT")) + "', " +
                                "'" + getStringValue(billItem.get("DISCOUNTINTEREST")) + "', " +
                                "'" + dueDate + "', " +
                                "'" + getStringValue(billItem.get("GSZT")) + "', " +
                                "'" + getStringValue(billItem.get("LRZX")) + "', " +
                                "'" + getStringValue(billItem.get("LRZXNAME")) + "', " +
                                "'" + getStringValue(billItem.get("CBZXBH")) + "', " +
                                "'" + getStringValue(billItem.get("CBZXMC")) + "', " +
                                "'" + payUnitCode + "', " + // ktnra
                                "'" + getStringValue(billItem.get("BILLNO")) + "', " +
                                "'" + getStringValue(billItem.get("SUBBILLSTARTSN")) + "', " +
                                "'" + getStringValue(billItem.get("SUBBILLENDSN")) + "', " +
                                "'" + getStringValue(billItem.get("DISCOUNTRATE")) + "', " +
                                "'', '', '', '', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "')";
                        
                        log.info("插入推送日志子表SQL " + (i+1) + "/" + billItems.size());
                        logService.info(LOG_CODE_COLLECTION, "插入推送日志子表SQL " + (i+1) + "/" + billItems.size() + " 长度: " + insertItemSql.length());
                        
                        int insertItemResult = DBUtil.executeUpdateSQL(insertItemSql);
                        log.info("插入推送日志子表结果: " + insertItemResult);
                        logService.info(LOG_CODE_COLLECTION, "插入推送日志子表结果: " + insertItemResult);
                    }
                    
                } catch (Exception e) {
                    log.error("记录推送结果到数据库异常: " + e.getMessage(), e);
                    logService.error(billNo, "记录推送结果到数据库异常: " + e.getMessage());
                }
                
            } catch (Exception e) {
                log.error("推送SAP异常: " + e.getMessage(), e);
                logService.error(billNo, "推送SAP异常: " + e.getMessage());
                
                // 记录推送异常到数据库
                try {
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);
                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_COLLECTION + "', " +
                                "pjcjms = '" + SCENE_DESC_COLLECTION + "', " +
                                "account_no = '" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + PUSH_STATUS_FAILED + "', " +
                                "PUSHLOG = '推送异常: " + e.getMessage() + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + existingLogId + "'";
                        
                        log.info("更新推送异常日志主表SQL");
                        logService.info(LOG_CODE_COLLECTION, "更新推送异常日志主表SQL");
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送异常日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE_COLLECTION, "更新推送异常日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE_COLLECTION, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE_COLLECTION, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 生成主表ID
                        String logId = UUID.randomUUID().toString();
                        
                        // 插入主表数据
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_COLLECTION + "', " +
                                "'" + SCENE_DESC_COLLECTION + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(receivingBill.get("SUMMARY")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITCODE")) + "', " +
                                "'" + getStringValue(receivingBill.get("RECEIVINGUNITNAME")) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(receivingBill.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + PUSH_STATUS_FAILED + "', " +
                                "'推送异常: " + e.getMessage() + "')";
                        
                        log.info("插入推送异常日志主表SQL");
                        logService.info(LOG_CODE_COLLECTION, "插入推送异常日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送异常日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE_COLLECTION, "插入推送异常日志主表结果: " + insertLogResult);
                    }
                    
                } catch (Exception ex) {
                    log.error("记录推送异常到数据库异常: " + ex.getMessage(), ex);
                    logService.error(billNo, "记录推送异常到数据库异常: " + ex.getMessage());
                }
            }

            // 为每个billNo单独刷新日志
            logService.flush();
        }

        log.info("应收票据托收到账票据推送SAP处理完成");
        logService.info(LOG_CODE_COLLECTION, "应收票据托收到账票据推送SAP处理完成");
    }

    /**
     * 应付票据到期兑付票据推送sap完成凭证推送
     */
    public void pushPayableBillToSap() {
        String jobId = "a5b7cd89-1234-5678-abcd-ef9012345678";
        String jobName = "应付票据到期兑付票据推送SAP";
        String lockId;
        
        try {
            // 获取分布式锁
            String moduleId = "JfskPayableBillSap";
            String funcId = "pushPayableBillToSap";
            String categoryId = "String";
            String dataID = "jtgk-Payable-Bill-Sap";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, 
                    new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, 
                            LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                log.error(jobName + "加锁失败");
                return;
            }
            
            lockId = lockResult.getLockId();
            log.info(jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发生异常：" + ex.toString());
            log.error(jobName + "加锁过程发生异常：", ex);
            return;
        }

        logService.init(LOG_CODE_PAYABLE);
        try {
            processPayableBillAndPushToSap(logService);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, ex.toString());
            log.error("应付票据到期兑付票据推送SAP过程发生异常：", ex);
            logService.error(LOG_CODE_PAYABLE, "应付票据到期兑付票据推送SAP过程发生异常：" +ExceptionUtils.getStackTrace(ex));
        }
        
        logService.flush();
        
        try {
            lockService.removeLock(lockId);
            log.info(jobName + "已解锁");
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
            log.error(jobName + "解锁过程发生异常：", ex);
        }
    }

    /**
     * 处理应付票据到期兑付并推送SAP
     * @param logService 日志服务
     */
    private void processPayableBillAndPushToSap(LogService logService) {
        log.info("开始处理应付票据到期兑付并推送SAP");
        logService.info(LOG_CODE_PAYABLE, "开始处理应付票据到期兑付并推送SAP");

        logService.info(LOG_CODE_PAYABLE, "开始处理应付票据到期兑付SAP相关参数");
        String selectSql = "select CODE, TXT01, TXT02, BIGTXT01 from IDD_DATADICTIONARY where CATEGORYID='b9badc01-6db5-5b10-5813-5311071bbccf' and TXT03='pjywpz'";
        logService.info(LOG_CODE_PAYABLE, "执行SQL：{}", selectSql);
        List<Map<String, Object>> rowsOfBizSys = DBUtil.querySql(selectSql);
        logService.info(LOG_CODE_PAYABLE, "查询结果：{}", JSON.toJSONString(rowsOfBizSys));
        if (rowsOfBizSys == null || rowsOfBizSys.isEmpty()) {
            logService.error(LOG_CODE_PAYABLE, "未找到业务系统配置");
            logService.flush();
            return;
        }

        // 环境配置
        String MANDT = rowsOfBizSys.get(0).get("CODE").toString();
        String sapBillPushUsername = rowsOfBizSys.get(0).get("TXT01").toString();
        String sapBillPushPassword = rowsOfBizSys.get(0).get("TXT02").toString();
        String sapBillPushUrl = rowsOfBizSys.get(0).get("BIGTXT01").toString();
        
        
        // 查询符合条件的业务支付申请单 - 应付票据到期兑付类型
        String querySql = "SELECT distinct br.ID, br.DOCNO, br.PAYACCOUNT as PAYACCOUNT, ba.ACCOUNTNO as PAYACCOUNTNO, bi.NAME_CHS AS BANKNAME, br.CURRENCY as CURRENCY, " +
                "br.PAYUNIT, org.NAME_CHS AS PAYUNITNAME, org.CODE AS PAYUNITCODE, payOrg.NAME_CHS AS RECEIVINGUNITNAME, payOrg.CODE AS RECEIVINGUNITCODE, " +
                "br.TIMESTAMPS_CREATEDON, user_info.NAME_CHS AS USERNAME, " +
                "br.SUMMARY AS SUMMARY, user_info.code as USERCODE, " + // 注意末尾逗号
                "jspl.ID AS existingLogId, jspl.PUSHSTATUS AS existingPushStatus " + // 新增字段
                "FROM BPBizpaymentrequest br " +
                "INNER JOIN ZJRLJL rl ON rl.BZDNM = br.ID " +
                "INNER JOIN ZJRLLX lx ON rl.RLLX = lx.ID AND lx.lxbh = '" + RECEIPT_TYPE_PAYABLE + "' " +
                "LEFT JOIN JTGKSAPPUSHBILLLOG jspl ON br.ID = jspl.pjid " + // 新增JOIN
                "LEFT JOIN BFADMINORGANIZATION org ON br.PAYUNIT = org.ID " +
                "LEFT JOIN BFPARTNER payOrg ON br.RECEIVINGUNIT = payOrg.ID " +
                "LEFT JOIN GSPUser user_info ON br.REQUESTUSER = user_info.ID " + // 注意这里是REQUESTUSER
                "LEFT JOIN BFBANKACCOUNTS ba ON br.PAYACCOUNT = ba.ID " +
                "LEFT JOIN BFBANK bi ON ba.BANK = bi.ID " +
                "WHERE br.docstatus = 5 " + // todo 保留用户原有注释
                "AND (jspl.ID IS NULL OR jspl.PUSHSTATUS NOT IN ('" + PUSH_STATUS_PUSHED + "', '" + PUSH_STATUS_SUCCESS + "')) " + // 新增条件
                "ORDER BY br.TIMESTAMPS_CREATEDON DESC";

        log.info("查询SQL: " + querySql);
        logService.info(LOG_CODE_PAYABLE, "查询SQL: " + querySql);
        
        List<Map<String, Object>> paymentRequests = DBUtil.querySql(querySql);
        log.info("查询结果: " + JSON.toJSONString(paymentRequests));
        logService.info(LOG_CODE_PAYABLE, "查询到" + (paymentRequests != null ? paymentRequests.size() : 0) + "条业务支付申请单");

        if (paymentRequests == null || paymentRequests.isEmpty()) {
            log.info("没有查询到符合条件的业务支付申请单");
            logService.info(LOG_CODE_PAYABLE, "没有查询到符合条件的业务支付申请单");
            return;
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        
        // 循环处理每一个业务支付申请单
        for (Map<String, Object> paymentRequest : paymentRequests) {
            String billId = (String) paymentRequest.get("ID");
            String billNo = (String) paymentRequest.get("DOCNO");

            // 为每个billNo单独初始化日志
            logService.init(billNo);

            // 获取业务发生日BUDAT并格式化为yyyyMMdd
            String businessDateSql = "SELECT TRANSACTIONDATE FROM BPBIZBANKTRANSDETAILS WHERE PARENTID = '" + billId + "'";
            List<Map<String, Object>> businessDateList = DBUtil.querySql(businessDateSql);
            String budatValue = ""; // 用于SAP XML和数据库日志。确保 dateFormat 在此作用域内已定义为 new SimpleDateFormat("yyyyMMdd")

            if (businessDateList != null && !businessDateList.isEmpty()) {
                Object transactionDateObj = businessDateList.get(0).get("TRANSACTIONDATE");
                if (transactionDateObj != null) {
                    if (transactionDateObj instanceof java.util.Date) { // 包括 java.sql.Date 和 java.sql.Timestamp
                        try {
                            // dateFormat 实例应为 new SimpleDateFormat("yyyyMMdd")
                            budatValue = dateFormat.format((java.util.Date) transactionDateObj);
                        } catch (Exception e) {
                            log.error("Error formatting TRANSACTIONDATE (Date object) for billId " + billId + " in " + LOG_CODE_PAYABLE + ": " + e.getMessage(), e);
                            logService.error(LOG_CODE_PAYABLE, "Error formatting TRANSACTIONDATE (Date object) for billId " + billId + ": " + e.getMessage());
                            // budatValue 保持为 ""
                        }
                    } else {
                        // 如果不是 Date 对象，尝试将其作为字符串解析
                        String transactionDateStr = transactionDateObj.toString().trim();
                        if (!transactionDateStr.isEmpty()) {
                            try {
                                Date parsedDate = null;
                                // 尝试常见的日期时间格式，如 "yyyy-MM-dd HH:mm:ss.S", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"
                                if (transactionDateStr.matches("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}(\\.\\d+)?$")) {
                                    parsedDate = new SimpleDateFormat(transactionDateStr.contains(".") ? "yyyy-MM-dd HH:mm:ss.S" : "yyyy-MM-dd HH:mm:ss").parse(transactionDateStr);
                                } else if (transactionDateStr.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
                                    parsedDate = new SimpleDateFormat("yyyy-MM-dd").parse(transactionDateStr);
                                }

                                if (parsedDate != null) {
                                    budatValue = dateFormat.format(parsedDate);
                                } else {
                                    log.warn("TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_PAYABLE + " did not match known date/timestamp patterns for parsing.");
                                    logService.warn(LOG_CODE_PAYABLE, "TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_PAYABLE + " did not match known date/timestamp patterns for parsing.");
                                }
                            } catch (java.text.ParseException pe) {
                                log.warn("Could not parse TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_PAYABLE + ". Error: " + pe.getMessage());
                                logService.warn(LOG_CODE_PAYABLE, "Could not parse TRANSACTIONDATE string '" + transactionDateStr + "' for billId " + billId + " in " + LOG_CODE_PAYABLE + ". Error: " + pe.getMessage());
                                // budatValue 保持为 ""
                            }
                        }
                    }
                }
            }
            // 原 businessDate 变量现在由 budatValue 替代。
            
            log.info("处理业务支付申请单: " + billNo);
            logService.info(billNo, "处理业务支付申请单: " + billNo);
            
            // 新的逻辑开始
            boolean needToUpdate = false;
            String existingLogId = getStringValue(paymentRequest.get("EXISTINGLOGID"));
            String existingPushStatus = getStringValue(paymentRequest.get("EXISTINGPUSHSTATUS"));
            
            if (existingLogId != null && !existingLogId.isEmpty()) {
                log.info("业务支付申请单" + billNo + "之前推送状态为 " + existingPushStatus + " (LogID: " + existingLogId + ")，将重新推送并更新记录。");
                logService.info(billNo, "业务支付申请单" + billNo + "之前推送状态为 " + existingPushStatus + " (LogID: " + existingLogId + ")，将重新推送并更新记录。");
                needToUpdate = true;
            } else {
                log.info("业务支付申请单" + billNo + "为新票据或之前未成功记录，将进行推送尝试。");
                logService.info(billNo, "业务支付申请单" + billNo + "为新票据或之前未成功记录，将进行推送尝试。");
            }
            // 旧的 checkPushedSql, pushedResults, needToPush 及相关 if/else 块 (原 lines approx 1637-1663) 已被上述逻辑和主查询优化所替代。
            // 新的逻辑结束
            
            // 查询支付申请单下的票据明细
            String queryBillItems = "SELECT b.ID as ID, b.PAYBILLID AS BILLID, b.PAYBILLNO as BILLNO, tmbp.BILLTYPE, tmbp.BILLAMT, tmbp.BILLDUEDATE, c.CODE AS CURRENCY, tmbp.TXT10 as GSZT, tmbp.TXT03 as LRZX, tmbp.TXT04 as LRZXNAME, " +
                    "tmbp.SUBBILLSTARTSN, tmbp.SUBBILLENDSN " +
                    "FROM BPBIZPAYMENTREQDETAIL b " +
                    "LEFT JOIN BFCURRENCY c ON b.CURRENCY = c.ID " +
                    "LEFT JOIN TMBILLPAYABLE tmbp ON b.PAYBILLID = tmbp.ID " +
                    "WHERE b.MID = '" + billId + "'";
            log.info("查询票据明细SQL: " + queryBillItems);
            logService.info(billNo, "查询票据明细SQL: " + queryBillItems);
            
            List<Map<String, Object>> billItems = DBUtil.querySql(queryBillItems);
            log.info("查询到" + (billItems != null ? billItems.size() : 0) + "条票据明细");
            logService.info(billNo, "查询到" + (billItems != null ? billItems.size() : 0) + "条票据明细");
            
            if (billItems == null || billItems.isEmpty()) {
                log.info("业务支付申请单" + billNo + "没有关联的票据明细，跳过处理");
                logService.info(billNo, "业务支付申请单" + billNo + "没有关联的票据明细，跳过处理");
                logService.flush(); // 在continue之前刷新日志
                continue;
            }

            // 取得银行科目
            String bankTitle = "select FK01 from BFBANKACCOUNTITEMS where parentid = '" + getStringValue(paymentRequest.get("PAYACCOUNT")) + "' and currency = '" + getStringValue(paymentRequest.get("CURRENCY")) + "'";
            List<Map<String, Object>> bankTitleList = DBUtil.querySql(bankTitle);
            String hkont = "";
            if (bankTitleList != null && !bankTitleList.isEmpty()) {
                hkont = getStringValue(bankTitleList.get(0).get("FK01"));
            }

            // 新增：获取 PAYUNITCODE (虽然此处变量名为 paymentRequest，但其 PAYUNITCODE 对应的是主单据的付款单位编码)
            // 确保 PAYUNITCODE 在 paymentRequest 中可用，此处假设它存在，如果不存在，则需要调整主查询
            String receivUnitCode = getStringValue(paymentRequest.get("RECEIVINGUNITCODE"));

            // 验证必填字段
            boolean hasInvalidFields = false;
            StringBuilder errorMsgBuilder = new StringBuilder();
            
            // 验证银行账号和银行科目
            String bankInfoValidation = validateBankInfo(getStringValue(paymentRequest.get("PAYACCOUNTNO")), hkont, RECEIPT_TYPE_PAYABLE);
            if (bankInfoValidation != null) {
                hasInvalidFields = true;
                errorMsgBuilder.append("银行信息校验失败: ").append(bankInfoValidation).append(" ");
            }
            
            for (Map<String, Object> billItem : billItems) {
                String validationResult = validateRequiredFields(billItem, RECEIPT_TYPE_PAYABLE);
                if (validationResult != null) {
                    hasInvalidFields = true;
                    errorMsgBuilder.append("票据号[")
                            .append(getStringValue(billItem.get("BILLNO")))
                            .append("]校验失败: ")
                            .append(validationResult)
                            .append(" ");
                }
            }
            
            if (hasInvalidFields) {
                String errorMsg = errorMsgBuilder.toString();
                log.error("业务支付申请单" + billNo + "存在字段验证错误: " + errorMsg);
                logService.error(billNo, "业务支付申请单" + billNo + "存在字段验证错误: " + errorMsg);
                
                // 记录推送失败到数据库
                try {
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);
                    

                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_PAYABLE + "', " +
                                "pjcjms = '" + SCENE_DESC_PAYABLE + "', " +
                                "account_no = '" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(paymentRequest.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(paymentRequest.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(paymentRequest.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + PUSH_STATUS_FAILED + "', " +
                                "PUSHLOG = '字段验证错误: " + errorMsg + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + existingLogId + "'";
                        
                        log.info("更新推送日志主表SQL: " + updateLogSql);
                        logService.info(LOG_CODE_PAYABLE, "更新推送日志主表SQL: " + updateLogSql);
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE_PAYABLE, "更新推送日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE_PAYABLE, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE_PAYABLE, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 插入新记录
                        String logId = UUID.randomUUID().toString();
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_PAYABLE + "', " +
                                "'" + SCENE_DESC_PAYABLE + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(paymentRequest.get("SUMMARY")) + "', " +
                                "'" + getStringValue(paymentRequest.get("USERCODE")) + "', " +
                                "'" + getStringValue(paymentRequest.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "', " +
                                "'" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + PUSH_STATUS_FAILED + "', " +
                                "'字段验证错误: " + errorMsg + "')";
                        
                        log.info("插入推送日志主表SQL: " + insertLogSql);
                        logService.info(LOG_CODE_PAYABLE, "插入推送日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE_PAYABLE, "插入推送日志主表结果: " + insertLogResult);
                    }
                } catch (Exception e) {
                    log.error("记录推送结果到数据库异常: " + e.getMessage(), e);
                    logService.error(billNo, "记录推送结果到数据库异常: " + e.getMessage());
                }

                logService.flush(); // 在continue之前刷新日志
                continue; // 跳过推送
            }
            
            // 构建推送SAP的XML
            StringBuffer xmlBuilder = new StringBuffer();
            xmlBuilder.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:goldwind.com:i_gsc:gsc_bill_book\">\n");
            xmlBuilder.append("   <soapenv:Header/>\n");
            xmlBuilder.append("   <soapenv:Body>\n");
            xmlBuilder.append("      <urn:mt_gsc_bill_book>\n");
            xmlBuilder.append("         <iv_msg_flag>GSClOUD</iv_msg_flag>\n");
            xmlBuilder.append("         <is_msg_head>\n");
            xmlBuilder.append("            <MANDT>" + MANDT + "</MANDT>\n");

            xmlBuilder.append("            <GUID>" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "</GUID>\n");
            
            xmlBuilder.append("            <PROXY_ID>" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "</PROXY_ID>\n");
            xmlBuilder.append("            <SYSTEM_ID>GSClOUD</SYSTEM_ID>\n");
            xmlBuilder.append("            <OPERATOR>" + getStringValue(paymentRequest.get("USERCODE")) + "</OPERATOR>\n");
            xmlBuilder.append("            <SPRAS>ZH</SPRAS>\n");
            xmlBuilder.append("            <INTERFACE_ID>si_gsc_bill_book</INTERFACE_ID>\n");
            xmlBuilder.append("            <SENDER>GSCloud</SENDER>\n");
            xmlBuilder.append("            <RECIVER>SAP</RECIVER>\n");
            xmlBuilder.append("            <SENDTIME>" + dateTimeFormat.format(new Date()) + "</SENDTIME>\n");
            xmlBuilder.append("         </is_msg_head>\n");
            xmlBuilder.append("         <is_header>\n");
            xmlBuilder.append("            <pjcjbh>" + SCENE_CODE_PAYABLE + "</pjcjbh>\n");
            xmlBuilder.append("            <pjcjms>" + SCENE_DESC_PAYABLE + "</pjcjms>\n");
            xmlBuilder.append("            <pjid>" + billId + "</pjid>\n");
            xmlBuilder.append("            <account_no>" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "</account_no>\n");
            xmlBuilder.append("            <hkont>" + hkont + "</hkont>\n");
            xmlBuilder.append("            <budat>" + budatValue + "</budat>\n");
            xmlBuilder.append("            <usecn>" + getStringValue(paymentRequest.get("SUMMARY")) + "</usecn>\n");
            xmlBuilder.append("            <user_code>" + getStringValue(paymentRequest.get("USERCODE")) + "</user_code>\n");
            xmlBuilder.append("            <user_name>" + getStringValue(paymentRequest.get("USERNAME")) + "</user_name>\n");
            xmlBuilder.append("            <apply_date>" + dateFormat.format(new Date()) + "</apply_date>\n");
            xmlBuilder.append("            <bukrs>" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "</bukrs>\n");
            xmlBuilder.append("            <butxt>" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "</butxt>\n");
            xmlBuilder.append("            <reserve_f1></reserve_f1>\n");
            xmlBuilder.append("            <reserve_f2></reserve_f2>\n");
            xmlBuilder.append("            <reserve_f3></reserve_f3>\n");
            xmlBuilder.append("         </is_header>\n");
            
            // 添加票据明细
            int lineNumber = 1;
            for (Map<String, Object> billItem : billItems) {
                xmlBuilder.append("         <it_row>\n");
                xmlBuilder.append("            <line_id>" + lineNumber + "</line_id>\n");
                xmlBuilder.append("            <bill_type>" + ("AC01".equals(getStringValue(billItem.get("BILLTYPE"))) ? "1" : "2") + "</bill_type>\n");
                xmlBuilder.append("            <waers>" + getStringValue(billItem.get("CURRENCY")) + "</waers>\n");
                xmlBuilder.append("            <bill_amount>" + formatBillAmount(billItem.get("BILLAMT")) + "</bill_amount>\n");
                xmlBuilder.append("            <fenum>" + getStringValue(billItem.get("DISCOUNTINTEREST")) + "</fenum>\n");
                
                // 格式化到期日期
                String dueDate = "";
                if (billItem.get("BILLDUEDATE") != null) {
                    try {
                        dueDate = dateFormat.format(billItem.get("BILLDUEDATE"));
                    } catch (Exception e) {
                        log.error("格式化到期日期异常", e);
                    }
                }
                xmlBuilder.append("            <zfbdt>" + dueDate + "</zfbdt>\n");
                xmlBuilder.append("            <busi>" + getStringValue(billItem.get("GSZT")) + "</busi>\n");
                xmlBuilder.append("            <prctr>" + getStringValue(billItem.get("LRZX")) + "</prctr>\n");
                xmlBuilder.append("            <ltext>" + getStringValue(billItem.get("LRZXNAME")) + "</ltext>\n");
                xmlBuilder.append("            <kostl></kostl>\n");
                xmlBuilder.append("            <cost_name></cost_name>\n");
                xmlBuilder.append("            <ktnra>" + receivUnitCode + "</ktnra>\n"); 
                xmlBuilder.append("            <bill_no>" + getStringValue(billItem.get("BILLNO")) + "</bill_no>\n");
                xmlBuilder.append("            <sub_bill_start_sn>" + getStringValue(billItem.get("SUBBILLSTARTSN")) + "</sub_bill_start_sn>\n");
                xmlBuilder.append("            <sub_bill_end_sn>" + getStringValue(billItem.get("SUBBILLENDSN")) + "</sub_bill_end_sn>\n");
                xmlBuilder.append("            <tran_rate></tran_rate>\n");
                xmlBuilder.append("            <reserve_f1></reserve_f1>\n");
                xmlBuilder.append("            <reserve_f2></reserve_f2>\n");
                xmlBuilder.append("            <reserve_f3></reserve_f3>\n");
                xmlBuilder.append("            <reserve_f4></reserve_f4>\n");
                xmlBuilder.append("            <reserve_f5></reserve_f5>\n");
                xmlBuilder.append("            <reserve_f6></reserve_f6>\n");
                xmlBuilder.append("         </it_row>\n");

                lineNumber++;
            }
            
            xmlBuilder.append("      </urn:mt_gsc_bill_book>\n");
            xmlBuilder.append("   </soapenv:Body>\n");
            xmlBuilder.append("</soapenv:Envelope>");
            
            String xmlData = xmlBuilder.toString();
            log.info("推送SAP的XML数据: " + xmlData);
            logService.info(billNo, "准备推送SAP的XML数据: " + xmlData);
            logService.info(billNo, "准备推送SAP的XML数据，长度: " + xmlData.length());

            logService.info(billNo, "系统配置参数：apiUrl={}, username={}, ssoBaseUrl={}", sapBillPushUrl, sapBillPushUsername, sapBillPushPassword);
    
            // 调用WebService推送数据到SAP
            try {
                String result = webServiceRequest(logService, billNo, sapBillPushUrl, "", xmlData, sapBillPushUsername, sapBillPushPassword, true);
                
                log.info("推送SAP结果: " + result);
                logService.info(billNo, "推送SAP结果: " + result);
                
                // 处理推送结果
                String pushStatus;
                String pushLog;
                
                // 判断是否成功推送
                // 情况1: 结果为空
                // 情况2: 返回的是空的SOAP信封，没有错误信息
                boolean isEmptySoapEnvelope = result != null && 
                    (result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header\\s*/>\\s*<SOAP:Body\\s*/>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header>\\s*</SOAP:Header>\\s*<SOAP:Body>\\s*</SOAP:Body>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<SOAP:Envelope[^>]*>\\s*<SOAP:Header/>\\s*<SOAP:Body/>\\s*</SOAP:Envelope>.*") ||
                     result.trim().matches("(?s).*<[^:]*:Envelope[^>]*>\\s*<[^:]*:Header\\s*/>\\s*<[^:]*:Body\\s*/>\\s*</[^:]*:Envelope>.*"));
                
                if (result == null || result.trim().isEmpty() || isEmptySoapEnvelope) {
                    // 已推送
                    pushStatus = PUSH_STATUS_PUSHED; // 修改点：原为 PUSH_STATUS_SUCCESS
                    pushLog = "已推送";
                    log.info("业务支付申请单" + billNo + "已推送至SAP");
                    logService.info(billNo, "业务支付申请单" + billNo + "已推送至SAP");
                } else {
                    // 推送失败
                    pushStatus = PUSH_STATUS_FAILED;
                    pushLog = "推送失败: " + result;
                    log.error("业务支付申请单" + billNo + "推送失败: " + result);
                    logService.error(billNo, "业务支付申请单" + billNo + "推送失败: " + result);
                }
                
                // 记录推送结果到数据库
                try {
                    // 根据是否需要更新确定记录ID
                    String logId = needToUpdate ? existingLogId : UUID.randomUUID().toString();
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);

                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_PAYABLE + "', " +
                                "pjcjms = '" + SCENE_DESC_PAYABLE + "', " +
                                "account_no = '" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(paymentRequest.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(paymentRequest.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(paymentRequest.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + pushStatus + "', " +
                                "PUSHLOG = '" + pushLog + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + logId + "'";
                        
                        log.info("更新推送日志主表SQL: " + updateLogSql);
                        logService.info(LOG_CODE_PAYABLE, "更新推送日志主表SQL: " + updateLogSql);
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE_PAYABLE, "更新推送日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + logId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE_PAYABLE, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE_PAYABLE, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 插入新记录
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_PAYABLE + "', " +
                                "'" + SCENE_DESC_PAYABLE + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(paymentRequest.get("SUMMARY")) + "', " +
                                "'" + getStringValue(paymentRequest.get("USERCODE")) + "', " +
                                "'" + getStringValue(paymentRequest.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "', " +
                                "'" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + pushStatus + "', " +
                                "'" + pushLog + "')";
                        
                        log.info("插入推送日志主表SQL: " + insertLogSql);
                        logService.info(LOG_CODE_PAYABLE, "插入推送日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE_PAYABLE, "插入推送日志主表结果: " + insertLogResult);
                    }
                    
                    // 插入子表数据
                    for (int i = 0; i < billItems.size(); i++) {
                        Map<String, Object> billItem = billItems.get(i);
                        String itemId = UUID.randomUUID().toString();
                        
                        String insertItemSql = "INSERT INTO JTGKSAPPUSHBILLLOGITEM (ID, PARENTID, bill_type, waers, " +
                                "bill_amount, fenum, zfbdt, busi, prctr, ltext, kostl, cost_name, ktnra, " +
                                "bill_no, sub_bill_start_sn, sub_bill_end_sn, tran_rate, " +
                                "reserve_f1, reserve_f2, reserve_f3, reserve_f4, reserve_f5, reserve_f6, " +
                                "TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY) VALUES (";
                        
                        String billType = "AC01".equals(getStringValue(billItem.get("BILLTYPE"))) ? "1" : "2";
                        String dueDate = "";
                        if (billItem.get("BILLDUEDATE") != null) {
                            try {
                                dueDate = dateFormat.format(billItem.get("BILLDUEDATE"));
                            } catch (Exception e) {
                                log.error("格式化到期日期异常", e);
                            }
                        }
                        
                        insertItemSql += "'" + itemId + "', " +
                                "'" + logId + "', " +
                                "'" + billType + "', " +
                                "'" + getStringValue(billItem.get("CURRENCY")) + "', " +
                                "'" + formatBillAmount(billItem.get("BILLAMT")) + "', " +
                                "'" + getStringValue(billItem.get("DISCOUNTINTEREST")) + "', " +
                                "'" + dueDate + "', " +
                                "'" + getStringValue(billItem.get("GSZT")) + "', " +
                                "'" + getStringValue(billItem.get("LRZX")) + "', " +
                                "'" + getStringValue(billItem.get("LRZXNAME")) + "', " +
                                "'" + getStringValue(billItem.get("CBZXBH")) + "', " +
                                "'" + getStringValue(billItem.get("CBZXMC")) + "', " +
                                "'" + receivUnitCode + "', " + // ktnra
                                "'" + getStringValue(billItem.get("BILLNO")) + "', " +
                                "'" + getStringValue(billItem.get("SUBBILLSTARTSN")) + "', " +
                                "'" + getStringValue(billItem.get("SUBBILLENDSN")) + "', " +
                                "'" + getStringValue(billItem.get("DISCOUNTRATE")) + "', " +
                                "'', '', '', '', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "')";
                        
                        log.info("插入推送日志子表SQL " + (i+1) + "/" + billItems.size());
                        logService.info(LOG_CODE_PAYABLE, "插入推送日志子表SQL " + (i+1) + "/" + billItems.size() + " 长度: " + insertItemSql.length());
                        
                        int insertItemResult = DBUtil.executeUpdateSQL(insertItemSql);
                        log.info("插入推送日志子表结果: " + insertItemResult);
                        logService.info(LOG_CODE_PAYABLE, "插入推送日志子表结果: " + insertItemResult);
                    }
                    
                } catch (Exception e) {
                    log.error("记录推送结果到数据库异常: " + e.getMessage(), e);
                    logService.error(billNo, "记录推送结果到数据库异常: " + e.getMessage());
                }
                
            } catch (Exception e) {
                log.error("推送SAP异常: " + e.getMessage(), e);
                logService.error(billNo, "推送SAP异常: " + e.getMessage());
                
                // 记录推送异常到数据库
                try {
                    Date now = new Date();
                    String userId = CAFContext.current.getUserId();
                    String formattedDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(now);
                    
                    if (needToUpdate) {
                        // 全字段更新
                        String updateLogSql = "UPDATE JTGKSAPPUSHBILLLOG SET " +
                                "pjcjbh = '" + SCENE_CODE_PAYABLE + "', " +
                                "pjcjms = '" + SCENE_DESC_PAYABLE + "', " +
                                "account_no = '" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "', " +
                                "hkont = '" + hkont + "', " +
                                "budat = '" + budatValue + "', " +
                                "usecn = '" + getStringValue(paymentRequest.get("SUMMARY")) + "', " +
                                "user_code = '" + getStringValue(paymentRequest.get("USERCODE")) + "', " +
                                "user_name = '" + getStringValue(paymentRequest.get("USERNAME")) + "', " +
                                "apply_date = '" + dateFormat.format(new Date()) + "', " +
                                "bukrs = '" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "', " +
                                "butxt = '" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "', " +
                                "GUID = '" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "PROXY_ID = '" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "reserve_f1 = '', " +
                                "reserve_f2 = '', " +
                                "reserve_f3 = '', " +
                                "PUSHSTATUS = '" + PUSH_STATUS_FAILED + "', " +
                                "PUSHLOG = '推送异常: " + e.getMessage() + "', " +
                                "TIMESTAMP_LASTCHANGEDON = '" + formattedDate + "', " +
                                "TIMESTAMP_LASTCHANGEDBY = '" + userId + "' " +
                                "WHERE ID = '" + existingLogId + "'";
                        
                        log.info("更新推送异常日志主表SQL");
                        logService.info(LOG_CODE_PAYABLE, "更新推送异常日志主表SQL");
                        
                        int updateLogResult = DBUtil.executeUpdateSQL(updateLogSql);
                        log.info("更新推送异常日志主表结果: " + updateLogResult);
                        logService.info(LOG_CODE_PAYABLE, "更新推送异常日志主表结果: " + updateLogResult);
                        
                        // 删除原有子表记录
                        String deleteItemsSql = "DELETE FROM JTGKSAPPUSHBILLLOGITEM WHERE PARENTID = '" + existingLogId + "'";
                        log.info("删除原有子表记录SQL: " + deleteItemsSql);
                        logService.info(LOG_CODE_PAYABLE, "删除原有子表记录SQL: " + deleteItemsSql);
                        
                        int deleteItemsResult = DBUtil.executeUpdateSQL(deleteItemsSql);
                        log.info("删除原有子表记录结果: " + deleteItemsResult);
                        logService.info(LOG_CODE_PAYABLE, "删除原有子表记录结果: " + deleteItemsResult);
                    } else {
                        // 生成主表ID
                        String logId = UUID.randomUUID().toString();
                        
                        // 插入主表数据
                        String insertLogSql = "INSERT INTO JTGKSAPPUSHBILLLOG (ID, pjcjbh, pjcjms, pjid, account_no, hkont, " +
                                "budat, usecn, user_code, user_name, apply_date, bukrs, butxt, " +
                                "GUID, PROXY_ID, reserve_f1, reserve_f2, reserve_f3, TIMESTAMP_CREATEDON, TIMESTAMP_CREATEDBY, " +
                                "TIMESTAMP_LASTCHANGEDON, TIMESTAMP_LASTCHANGEDBY, PUSHSTATUS, PUSHLOG) VALUES (";
                        
                        insertLogSql += "'" + logId + "', " +
                                "'" + SCENE_CODE_PAYABLE + "', " +
                                "'" + SCENE_DESC_PAYABLE + "', " +
                                "'" + billId + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYACCOUNTNO")) + "', " +
                                "'" + hkont + "', " +
                                "'" + budatValue + "', " +
                                "'" + getStringValue(paymentRequest.get("SUMMARY")) + "', " +
                                "'" + getStringValue(paymentRequest.get("USERCODE")) + "', " +
                                "'" + getStringValue(paymentRequest.get("USERNAME")) + "', " +
                                "'" + dateFormat.format(new Date()) + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYUNITCODE")) + "', " +
                                "'" + getStringValue(paymentRequest.get("PAYUNITNAME")) + "', " +
                                "'" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'" + getStringValue(paymentRequest.get("DOCNO")) + dateTimeFormat.format(new Date()) + "', " +
                                "'', '', '', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + formattedDate + "', " +
                                "'" + userId + "', " +
                                "'" + PUSH_STATUS_FAILED + "', " +
                                "'推送异常: " + e.getMessage() + "')";
                        
                        log.info("插入推送异常日志主表SQL");
                        logService.info(LOG_CODE_PAYABLE, "插入推送异常日志主表SQL长度: " + insertLogSql.length());
                        
                        int insertLogResult = DBUtil.executeUpdateSQL(insertLogSql);
                        log.info("插入推送异常日志主表结果: " + insertLogResult);
                        logService.info(LOG_CODE_PAYABLE, "插入推送异常日志主表结果: " + insertLogResult);
                    }
                    
                } catch (Exception ex) {
                    log.error("记录推送异常到数据库异常: " + ex.getMessage(), ex);
                    logService.error(billNo, "记录推送异常到数据库异常: " + ex.getMessage());
                }
            }

            // 为每个billNo单独刷新日志
            logService.flush();
        }

        log.info("应付票据到期兑付票据推送SAP处理完成");
        logService.info(LOG_CODE_PAYABLE, "应付票据到期兑付票据推送SAP处理完成");
    }

} 